# 🗃️ Airtable Integration Guide
## Google Ads Data Extractor - Enhanced Edition

### 📊 **Integration Overview**

Your Google Ads Data Extractor application has been successfully enhanced with comprehensive Airtable integration capabilities. This allows you to:

- **Sync Google Ads data directly** to your existing Airtable "Google Ads" table
- **Auto-sync after extraction** for seamless workflow automation
- **Replace or append data** based on your preferences
- **Monitor sync status** with real-time logging
- **Test connections** before syncing data

### 🏗️ **Current Airtable Setup Analysis**

**Base**: QuickFix (`app7ffftdM6e3yekG`)
**Table**: Google Ads (`tblRBXdh6L6zm9CZn`)

**Perfect Schema Match**:
```
✅ Date (Date field)
✅ Campaign ID (Number field)
✅ Campaign Name (Single Select with 100+ campaign options)
✅ Cost (Number field, 6 decimal precision)
✅ Impressions (Number field, integer)
✅ Clicks (Number field, integer)
✅ Conversions (Number field, 6 decimal precision)
✅ CTR (Number field, 8 decimal precision)
✅ CPC (Number field, 8 decimal precision)
✅ Conv. Rate (Number field, 8 decimal precision)
✅ Cost per Conv. (Number field, 8 decimal precision)
```

**Current Data**: Your table already contains real Google Ads data with campaigns like:
- RSA Workaround Repair campaigns (Mobile, Foley, Daphne locations)
- RL Buybacks campaigns
- RL Device Sale campaigns
- Pmax campaigns

### 🚀 **New Features Added**

#### **1. Airtable Manager Class**
- **API Connection Management**: Secure API key handling
- **Data Upload**: Batch processing (10 records per batch)
- **Data Validation**: Automatic data type conversion and validation
- **Error Handling**: Comprehensive error reporting
- **Record Management**: Get, create, and delete operations

#### **2. New Airtable Tab in UI**
- **API Configuration**: Secure API key input with masking
- **Connection Testing**: One-click connection verification
- **Sync Options**: Auto-sync and data replacement settings
- **Sync Actions**: Manual sync, view data, and clear data buttons
- **Status Monitoring**: Real-time sync status with detailed logging

#### **3. Enhanced Configuration**
- **Persistent Settings**: Airtable settings saved in configuration
- **Auto-sync Options**: Configurable automatic syncing
- **Security**: API keys stored securely in configuration

### 📋 **Setup Instructions**

#### **Step 1: Get Airtable API Key**
1. Go to [Airtable Account Settings](https://airtable.com/account)
2. Navigate to "Developer" section
3. Create a new Personal Access Token with:
   - **Scopes**: `data.records:read`, `data.records:write`
   - **Access**: Your "QuickFix" base
4. Copy the generated token

#### **Step 2: Configure in Application**
1. Open the Google Ads Data Extractor
2. Go to the **🗃️ Airtable** tab
3. Enter your API key in the "Airtable API Key" field
4. Click **🔗 Test Connection** to verify setup
5. Configure sync options:
   - ✅ **Auto-sync after data extraction** (recommended)
   - ⚠️ **Replace existing data** (use carefully)

#### **Step 3: Save Settings**
1. Go to **⚙️ Settings** tab
2. Click **💾 Save Settings** to persist configuration

### 🔄 **Usage Workflows**

#### **Workflow 1: Manual Sync**
1. Extract Google Ads data using **🚀 Extract Data**
2. Go to **🗃️ Airtable** tab
3. Click **🔄 Sync Current Data**
4. Monitor progress in the status log

#### **Workflow 2: Auto-Sync (Recommended)**
1. Enable "Auto-sync after data extraction" in Airtable tab
2. Extract Google Ads data using **🚀 Extract Data**
3. Data automatically syncs to Airtable after extraction
4. Check logs for sync confirmation

#### **Workflow 3: Data Management**
- **View Data**: Click **📊 View Airtable Data** to open Airtable in browser
- **Clear Data**: Click **🗑️ Clear Airtable Data** to remove all records
- **Replace Data**: Enable "Replace existing data" before syncing

### 🛠️ **Technical Implementation Details**

#### **Data Mapping**
```python
# Python DataFrame → Airtable Fields
{
    "Date": row['Date'],                    # Date field
    "Campaign ID": int(row['Campaign ID']), # Number field
    "Campaign Name": str(row['Campaign Name']), # Single Select
    "Cost": float(row['Cost']),             # Number field
    "Impressions": int(row['Impressions']), # Number field
    "Clicks": int(row['Clicks']),           # Number field
    "Conversions": float(row['Conversions']), # Number field
    "CTR": float(row['CTR']),               # Number field
    "CPC": float(row['CPC']),               # Number field
    "Conv. Rate": float(row['Conv. Rate']), # Number field
    "Cost per Conv.": float(row['Cost per Conv.']) # Number field
}
```

#### **Batch Processing**
- **Batch Size**: 10 records per API call (Airtable limit)
- **Error Handling**: Individual batch error reporting
- **Progress Tracking**: Real-time upload progress

#### **API Endpoints Used**
- **GET**: `/v0/{baseId}/{tableId}` - Retrieve records
- **POST**: `/v0/{baseId}/{tableId}` - Create records
- **DELETE**: `/v0/{baseId}/{tableId}` - Delete records

### 🔒 **Security Features**

- **API Key Masking**: Keys displayed as asterisks in UI
- **Secure Storage**: Keys encrypted in configuration file
- **Connection Validation**: API key tested before operations
- **Error Sanitization**: Sensitive data removed from error messages

### 📈 **Benefits**

1. **Centralized Data**: All Google Ads data in one Airtable workspace
2. **Real-time Sync**: Immediate data availability after extraction
3. **Multi-client Support**: Ready for multiple client configurations
4. **Data Integrity**: Automatic validation and error handling
5. **Workflow Automation**: Seamless integration with existing processes

### 🎯 **Next Steps & Recommendations**

#### **Immediate Actions**
1. Set up Airtable API key and test connection
2. Enable auto-sync for streamlined workflow
3. Test with small data sets first

#### **Future Enhancements**
1. **Multi-table Support**: Sync to different tables for different clients
2. **Field Mapping**: Custom field mapping configuration
3. **Incremental Sync**: Only sync new/changed records
4. **Sync Scheduling**: Automated periodic syncing
5. **Data Validation Rules**: Custom validation before sync

### 🆘 **Troubleshooting**

#### **Common Issues**
- **Connection Failed**: Check API key and permissions
- **Upload Errors**: Verify data format and field types
- **Sync Timeout**: Reduce batch size for large datasets
- **Permission Denied**: Ensure API token has write access

#### **Support**
- Check the status log in the Airtable tab for detailed error messages
- Test connection before syncing large datasets
- Use "Replace existing data" carefully to avoid data loss

---

**🎉 Your Google Ads Data Extractor is now fully integrated with Airtable!**
