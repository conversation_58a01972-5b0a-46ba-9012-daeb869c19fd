# 🗃️ Airtable Integration Guide
## Google Ads Data Extractor - Enhanced Edition

### 📊 **Integration Overview**

Your Google Ads Data Extractor application has been successfully enhanced with comprehensive Airtable integration capabilities. This allows you to:

- **Sync Google Ads data directly** to your existing Airtable "Google Ads" table
- **Auto-sync after extraction** for seamless workflow automation
- **Replace or append data** based on your preferences
- **Monitor sync status** with real-time logging
- **Test connections** before syncing data

### 🏗️ **Current Airtable Setup Analysis**

**Base**: QuickFix (`app7ffftdM6e3yekG`)
**Table**: Google Ads (`tblRBXdh6L6zm9CZn`)

**Perfect Schema Match**:
```
✅ Date (Date field)
✅ Campaign ID (Number field)
✅ Campaign Name (Single Select with 100+ campaign options)
✅ Cost (Number field, 6 decimal precision)
✅ Impressions (Number field, integer)
✅ Clicks (Number field, integer)
✅ Conversions (Number field, 6 decimal precision)
✅ CTR (Number field, 8 decimal precision)
✅ CPC (Number field, 8 decimal precision)
✅ Conv. Rate (Number field, 8 decimal precision)
✅ Cost per Conv. (Number field, 8 decimal precision)
```

**Current Data**: Your table already contains real Google Ads data with campaigns like:
- RSA Workaround Repair campaigns (Mobile, Foley, Daphne locations)
- RL Buybacks campaigns
- RL Device Sale campaigns
- Pmax campaigns

### 🚀 **New Features Added**

#### **1. Airtable Manager Class**
- **API Connection Management**: Secure API key handling
- **Data Upload**: Batch processing (10 records per batch)
- **Data Validation**: Automatic data type conversion and validation
- **Error Handling**: Comprehensive error reporting
- **Record Management**: Get, create, and delete operations

#### **2. New Airtable Tab in UI**
- **API Configuration**: Secure API key input with masking
- **Connection Testing**: One-click connection verification
- **Sync Options**: Auto-sync and data replacement settings
- **Sync Actions**: Manual sync, view data, and clear data buttons
- **Status Monitoring**: Real-time sync status with detailed logging

#### **3. Enhanced Configuration**
- **Persistent Settings**: Airtable settings saved in configuration
- **Auto-sync Options**: Configurable automatic syncing
- **Security**: API keys stored securely in configuration

### 📋 **Setup Instructions**

#### **Step 1: Get Airtable API Key**
1. Go to [Airtable Account Settings](https://airtable.com/account)
2. Navigate to "Developer" section
3. Create a new Personal Access Token with:
   - **Scopes**: `data.records:read`, `data.records:write`
   - **Access**: Your "QuickFix" base
4. Copy the generated token

#### **Step 2: Configure in Application**
1. Open the Google Ads Data Extractor
2. Go to the **🗃️ Airtable** tab
3. Enter your API key in the "Airtable API Key" field
4. Click **🔗 Test Connection** to verify setup
5. Configure sync options:
   - ✅ **Auto-sync after data extraction** (recommended)
   - ⚠️ **Replace existing data** (use carefully)

#### **Step 3: Save Settings**
1. Go to **⚙️ Settings** tab
2. Click **💾 Save Settings** to persist configuration

### 🔄 **Sync Modes & Usage Workflows**

#### **🔄 Incremental Mode (Recommended)**
**Perfect for your scenario: 1970 → 2484 records**
- **What it does**: Automatically detects and skips existing records
- **How it works**: Compares Date + Campaign ID combinations
- **Your use case**: Will add only records 1971-2484, skipping existing 1-1970
- **Benefits**: Fast, efficient, no duplicates

#### **➕ Append Mode**
- **What it does**: Adds all extracted records as new entries
- **When to use**: When you want to keep historical duplicates
- **Warning**: May create duplicate records

#### **🔄 Replace Mode**
- **What it does**: Clears all existing data, then adds new records
- **When to use**: Complete data refresh scenarios
- **Warning**: Deletes ALL existing data first

### 📋 **Step-by-Step: Your Incremental Update**

#### **For Your 1970 → 2484 Records Scenario:**

1. **Setup** (One-time):
   - Configure Airtable API key
   - Set sync mode to **🔄 Incremental** (default)
   - Enable auto-sync (optional)

2. **Extract Your 2484 Records**:
   - Use **🚀 Extract Data** with your desired date range
   - App will extract all 2484 records from Google Ads

3. **Incremental Sync**:
   - App automatically detects your existing 1970 records
   - Skips existing records (1-1970)
   - Adds only new records (1971-2484)
   - Shows: "Uploaded 514 records (skipped 1970 existing)"

#### **Workflow Options:**

**Option A: Manual Sync**
1. Extract Google Ads data using **🚀 Extract Data**
2. Go to **🗃️ Airtable** tab
3. Ensure **🔄 Incremental** mode is selected
4. Click **🔄 Sync Current Data**
5. Monitor progress: "Uploaded X records (skipped Y existing)"

**Option B: Auto-Sync (Recommended)**
1. Set sync mode to **🔄 Incremental**
2. Enable "Auto-sync after data extraction"
3. Extract Google Ads data using **🚀 Extract Data**
4. Data automatically syncs incrementally after extraction
5. Check logs for confirmation

**Option C: Data Management**
- **📊 View Airtable Data**: Open Airtable in browser
- **📊 Show Statistics**: View record counts and totals
- **🗑️ Clear Airtable Data**: Remove all records (use carefully)

### 🛠️ **Technical Implementation Details**

#### **Incremental Update Logic**
```python
# Duplicate Detection Algorithm
def is_new_record(row):
    date = row['Date']
    campaign_id = row['Campaign ID']
    key = f"{date}_{campaign_id}"
    return key not in existing_keys

# Process:
1. Get all existing records from Airtable
2. Create set of existing keys (Date_CampaignID)
3. Filter new data to exclude existing keys
4. Upload only new records
```

#### **Data Mapping**
```python
# Python DataFrame → Airtable Fields
{
    "Date": row['Date'],                    # Date field
    "Campaign ID": int(row['Campaign ID']), # Number field
    "Campaign Name": str(row['Campaign Name']), # Single Select
    "Cost": float(row['Cost']),             # Number field
    "Impressions": int(row['Impressions']), # Number field
    "Clicks": int(row['Clicks']),           # Number field
    "Conversions": float(row['Conversions']), # Number field
    "CTR": float(row['CTR']),               # Number field
    "CPC": float(row['CPC']),               # Number field
    "Conv. Rate": float(row['Conv. Rate']), # Number field
    "Cost per Conv.": float(row['Cost per Conv.']) # Number field
}
```

#### **Performance Features**
- **Batch Processing**: 10 records per API call (Airtable limit)
- **Pagination**: Handles large datasets with automatic pagination
- **Duplicate Detection**: Efficient key-based comparison
- **Error Handling**: Individual batch error reporting
- **Progress Tracking**: Real-time upload progress with skip counts

#### **API Endpoints Used**
- **GET**: `/v0/{baseId}/{tableId}` - Retrieve records (with pagination)
- **POST**: `/v0/{baseId}/{tableId}` - Create records (batch)
- **DELETE**: `/v0/{baseId}/{tableId}` - Delete records (batch)

### 🔒 **Security Features**

- **API Key Masking**: Keys displayed as asterisks in UI
- **Secure Storage**: Keys encrypted in configuration file
- **Connection Validation**: API key tested before operations
- **Error Sanitization**: Sensitive data removed from error messages

### 📈 **Benefits**

1. **Centralized Data**: All Google Ads data in one Airtable workspace
2. **Real-time Sync**: Immediate data availability after extraction
3. **Multi-client Support**: Ready for multiple client configurations
4. **Data Integrity**: Automatic validation and error handling
5. **Workflow Automation**: Seamless integration with existing processes

### 🎯 **Next Steps & Recommendations**

#### **Immediate Actions**
1. Set up Airtable API key and test connection
2. Enable auto-sync for streamlined workflow
3. Test with small data sets first

#### **Future Enhancements**
1. **Multi-table Support**: Sync to different tables for different clients
2. **Field Mapping**: Custom field mapping configuration
3. **Incremental Sync**: Only sync new/changed records
4. **Sync Scheduling**: Automated periodic syncing
5. **Data Validation Rules**: Custom validation before sync

### 🆘 **Troubleshooting**

#### **Common Issues**
- **Connection Failed**: Check API key and permissions
- **Upload Errors**: Verify data format and field types
- **Sync Timeout**: Reduce batch size for large datasets
- **Permission Denied**: Ensure API token has write access

#### **Support**
- Check the status log in the Airtable tab for detailed error messages
- Test connection before syncing large datasets
- Use "Replace existing data" carefully to avoid data loss

---

**🎉 Your Google Ads Data Extractor is now fully integrated with Airtable!**
