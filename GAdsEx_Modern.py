import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import ttkbootstrap as ttk_bootstrap
from ttkbootstrap.constants import *
# Removed tkcalendar import due to compatibility issues
import threading
import os
import sys
import json
import datetime
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
import pandas as pd
import tempfile
import time
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

# Check if we need to install required packages
try:
    from googleapiclient.discovery import build
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.oauth2.credentials import Credentials
    import openpyxl  # For Excel export
    import requests  # For Airtable API
except ImportError:
    print("Installing required packages...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install",
                          "google-api-python-client", "google-auth-httplib2",
                          "google-auth-oauthlib", "google-ads", "pandas",
                          "openpyxl", "requests"])

    # Now import again after installation
    from googleapiclient.discovery import build
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.oauth2.credentials import Credentials
    import openpyxl
    import requests

# Constants
CLIENT_ID = "774457045565-fo4vc6ge3jct4jkafj33h19e4uv79kd1.apps.googleusercontent.com"
CLIENT_SECRET = "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"
SCOPES = ['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/adwords']
CONFIG_FILE = "ads_extractor_config.json"

# Airtable Configuration
AIRTABLE_BASE_ID = "app7ffftdM6e3yekG"  # QuickFix base
AIRTABLE_GOOGLE_ADS_TABLE_ID = "tblRBXdh6L6zm9CZn"  # Google Ads table
AIRTABLE_API_URL = "https://api.airtable.com/v0"

# Modern color scheme
COLORS = {
    'primary': '#2E86AB',
    'secondary': '#A23B72',
    'success': '#28A745',
    'warning': '#FFC107',
    'danger': '#DC3545',
    'info': '#17A2B8',
    'light': '#F8F9FA',
    'dark': '#343A40',
    'background': '#FFFFFF',
    'surface': '#F5F5F5'
}

# Date range presets
DATE_PRESETS = {
    "Today": lambda: (datetime.datetime.now().strftime('%Y-%m-%d'),
                     datetime.datetime.now().strftime('%Y-%m-%d')),
    "Yesterday": lambda: ((datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                         (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')),
    "Last 7 days": lambda: ((datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'),
                           datetime.datetime.now().strftime('%Y-%m-%d')),
    "Last 30 days": lambda: ((datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'),
                            datetime.datetime.now().strftime('%Y-%m-%d')),
    "Last 90 days": lambda: ((datetime.datetime.now() - datetime.timedelta(days=90)).strftime('%Y-%m-%d'),
                            datetime.datetime.now().strftime('%Y-%m-%d')),
    "This month": lambda: (datetime.datetime.now().replace(day=1).strftime('%Y-%m-%d'),
                          datetime.datetime.now().strftime('%Y-%m-%d')),
    "Last month": lambda: ((datetime.datetime.now().replace(day=1) - datetime.timedelta(days=1)).replace(day=1).strftime('%Y-%m-%d'),
                          (datetime.datetime.now().replace(day=1) - datetime.timedelta(days=1)).strftime('%Y-%m-%d')),
    "Custom": lambda: (None, None)
}

class ModernProgressDialog:
    """Modern progress dialog with cancel functionality"""

    def __init__(self, parent, title="Processing...", message="Please wait..."):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (150 // 2)
        self.dialog.geometry(f"400x150+{x}+{y}")

        self.cancelled = False

        # Main frame
        main_frame = ttk_bootstrap.Frame(self.dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # Message label
        self.message_label = ttk_bootstrap.Label(main_frame, text=message, font=('Arial', 11))
        self.message_label.pack(pady=(0, 15))

        # Progress bar
        self.progress = ttk_bootstrap.Progressbar(
            main_frame,
            mode='indeterminate',
            bootstyle="info-striped",
            length=300
        )
        self.progress.pack(pady=(0, 15))
        self.progress.start(10)

        # Cancel button
        self.cancel_btn = ttk_bootstrap.Button(
            main_frame,
            text="Cancel",
            command=self.cancel,
            bootstyle="outline-danger"
        )
        self.cancel_btn.pack()

    def update_message(self, message):
        self.message_label.config(text=message)
        self.dialog.update()

    def cancel(self):
        self.cancelled = True
        self.close()

    def close(self):
        self.progress.stop()
        self.dialog.destroy()

class ModernTooltip:
    """Modern tooltip implementation"""

    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.show_tooltip)
        self.widget.bind("<Leave>", self.hide_tooltip)

    def show_tooltip(self, event=None):
        if self.tooltip:
            return

        x, y, _, _ = self.widget.bbox("insert")
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25

        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry(f"+{x}+{y}")

        label = ttk_bootstrap.Label(
            self.tooltip,
            text=self.text,
            background="#333333",
            foreground="white",
            padding=5,
            font=('Arial', 9)
        )
        label.pack()

    def hide_tooltip(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None



class ModernKPICard:
    """Modern KPI card widget"""

    def __init__(self, parent, title, value, change=None, color="primary"):
        self.frame = ttk_bootstrap.Frame(parent, padding=15, bootstyle=f"{color}")

        # Title
        title_label = ttk_bootstrap.Label(
            self.frame,
            text=title,
            font=('Arial', 10, 'bold'),
            bootstyle=f"{color}-inverse"
        )
        title_label.pack(anchor='w')

        # Value
        self.value_label = ttk_bootstrap.Label(
            self.frame,
            text=value,
            font=('Arial', 18, 'bold'),
            bootstyle=f"{color}-inverse"
        )
        self.value_label.pack(anchor='w', pady=(5, 0))

        # Change indicator
        if change:
            change_color = "success" if change.startswith('+') else "danger"
            change_label = ttk_bootstrap.Label(
                self.frame,
                text=change,
                font=('Arial', 9),
                bootstyle=f"{change_color}-inverse"
            )
            change_label.pack(anchor='w')

    def update_value(self, value, change=None):
        self.value_label.config(text=value)

    def pack(self, **kwargs):
        self.frame.pack(**kwargs)

    def grid(self, **kwargs):
        self.frame.grid(**kwargs)

class AirtableManager:
    """Airtable API integration manager"""

    def __init__(self, api_key=None):
        self.api_key = api_key
        self.base_id = AIRTABLE_BASE_ID
        self.google_ads_table_id = AIRTABLE_GOOGLE_ADS_TABLE_ID
        self.ghl_table_id = "tblcdFVUC3zJrbmNf"  # GHL table
        self.pos_table_id = "tblHyyZHUsTdEb3BL"  # POS table
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        } if api_key else None

    def set_api_key(self, api_key):
        """Set Airtable API key"""
        self.api_key = api_key
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

    def test_connection(self):
        """Test Airtable API connection"""
        if not self.api_key:
            return False, "API key not set"

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"
            params = {'maxRecords': 1}
            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                return True, "Connection successful"
            else:
                return False, f"API Error: {response.status_code} - {response.text}"
        except Exception as e:
            return False, f"Connection error: {str(e)}"

    def upload_google_ads_data(self, dataframe, batch_size=10, mode="append"):
        """Upload Google Ads data to Airtable with different modes"""
        if not self.api_key:
            raise Exception("Airtable API key not set")

        if dataframe.empty:
            return 0, "No data to upload", 0

        url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"
        uploaded_count = 0
        skipped_count = 0
        errors = []

        # Get existing records for duplicate detection if in incremental mode
        existing_keys = set()
        if mode == "incremental":
            existing_keys = self.get_existing_record_keys()

        # Filter data based on mode
        filtered_data = dataframe.copy()

        if mode == "incremental":
            # Filter out records that already exist
            def is_new_record(row):
                date = row['Date'] if pd.notna(row['Date']) else None
                campaign_id = row['Campaign ID'] if pd.notna(row['Campaign ID']) else None

                if date and campaign_id:
                    key = f"{date}_{int(campaign_id)}"
                    return key not in existing_keys
                return True

            mask = filtered_data.apply(is_new_record, axis=1)
            filtered_data = filtered_data[mask]
            skipped_count = len(dataframe) - len(filtered_data)

        if filtered_data.empty:
            return 0, "No new records to upload", skipped_count

        # Process data in batches
        for i in range(0, len(filtered_data), batch_size):
            batch = filtered_data.iloc[i:i+batch_size]
            records = []

            for _, row in batch.iterrows():
                # Map DataFrame columns to Airtable fields
                record = {
                    "fields": {
                        "Date": row['Date'] if pd.notna(row['Date']) else None,
                        "Campaign ID": int(row['Campaign ID']) if pd.notna(row['Campaign ID']) else None,
                        "Campaign Name": str(row['Campaign Name']) if pd.notna(row['Campaign Name']) else None,
                        "Cost": float(row['Cost']) if pd.notna(row['Cost']) else 0,
                        "Impressions": int(row['Impressions']) if pd.notna(row['Impressions']) else 0,
                        "Clicks": int(row['Clicks']) if pd.notna(row['Clicks']) else 0,
                        "Conversions": float(row['Conversions']) if pd.notna(row['Conversions']) else 0,
                        "CTR": float(row['CTR']) if pd.notna(row['CTR']) else 0,
                        "CPC": float(row['CPC']) if pd.notna(row['CPC']) else 0,
                        "Conv. Rate": float(row['Conv. Rate']) if pd.notna(row['Conv. Rate']) else 0,
                        "Cost per Conv.": float(row['Cost per Conv.']) if pd.notna(row['Cost per Conv.']) else 0
                    }
                }
                records.append(record)

            # Send batch to Airtable
            try:
                payload = {"records": records}
                response = requests.post(url, headers=self.headers, json=payload)

                if response.status_code == 200:
                    uploaded_count += len(records)
                else:
                    error_msg = f"Batch {i//batch_size + 1} failed: {response.status_code} - {response.text}"
                    errors.append(error_msg)
            except Exception as e:
                error_msg = f"Batch {i//batch_size + 1} error: {str(e)}"
                errors.append(error_msg)

        return uploaded_count, errors, skipped_count

    def upload_ghl_data(self, dataframe, batch_size=10, mode="append"):
        """Upload GHL lead data to Airtable"""
        if not self.api_key:
            raise Exception("Airtable API key not set")

        if dataframe.empty:
            return 0, "No data to upload", 0

        url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.ghl_table_id}"
        uploaded_count = 0
        skipped_count = 0
        errors = []

        # Get existing records for duplicate detection if in incremental mode
        existing_keys = set()
        if mode == "incremental":
            existing_keys = self.get_existing_ghl_record_keys()

        # Filter data based on mode
        filtered_data = dataframe.copy()

        if mode == "incremental":
            # Filter out records that already exist (based on Contact ID or email+phone combination)
            def is_new_ghl_record(row):
                contact_id = row.get('Contact ID') if pd.notna(row.get('Contact ID', '')) else None
                email = row.get('email', '') if pd.notna(row.get('email', '')) else ''
                phone = row.get('phone', '') if pd.notna(row.get('phone', '')) else ''

                if contact_id:
                    return contact_id not in existing_keys
                elif email and phone:
                    key = f"{email}_{phone}"
                    return key not in existing_keys
                return True

            mask = filtered_data.apply(is_new_ghl_record, axis=1)
            filtered_data = filtered_data[mask]
            skipped_count = len(dataframe) - len(filtered_data)

        if filtered_data.empty:
            return 0, "No new records to upload", skipped_count

        # Process data in batches
        for i in range(0, len(filtered_data), batch_size):
            batch = filtered_data.iloc[i:i+batch_size]
            records = []

            for _, row in batch.iterrows():
                # Map DataFrame columns to Airtable fields
                record = {
                    "fields": {
                        "contact name": str(row.get('contact name', '')) if pd.notna(row.get('contact name', '')) else None,
                        "Location": str(row.get('Location', '')) if pd.notna(row.get('Location', '')) else None,
                        "phone": str(row.get('phone', '')) if pd.notna(row.get('phone', '')) else None,
                        "email": str(row.get('email', '')) if pd.notna(row.get('email', '')) else None,
                        "pipeline": str(row.get('pipeline', '')) if pd.notna(row.get('pipeline', '')) else None,
                        "stage": str(row.get('stage', '')) if pd.notna(row.get('stage', '')) else None,
                        "Lead Value": float(row.get('Lead Value', 0)) if pd.notna(row.get('Lead Value', 0)) else 0,
                        "Date Created": str(row.get('Date Created', '')) if pd.notna(row.get('Date Created', '')) else None,
                        "Traffic Source": str(row.get('Traffic Source', '')) if pd.notna(row.get('Traffic Source', '')) else None,
                        "Channel": str(row.get('Channel', '')) if pd.notna(row.get('Channel', '')) else None,
                        "Conversion Event": str(row.get('Conversion Event', '')) if pd.notna(row.get('Conversion Event', '')) else None,
                        "Opportunity ID": str(row.get('Opportunity ID', '')) if pd.notna(row.get('Opportunity ID', '')) else None,
                        "Contact ID": str(row.get('Contact ID', '')) if pd.notna(row.get('Contact ID', '')) else None
                    }
                }
                # Remove None values
                record["fields"] = {k: v for k, v in record["fields"].items() if v is not None}
                records.append(record)

            # Send batch to Airtable
            try:
                payload = {"records": records}
                response = requests.post(url, headers=self.headers, json=payload)

                if response.status_code == 200:
                    uploaded_count += len(records)
                else:
                    error_msg = f"Batch {i//batch_size + 1} failed: {response.status_code} - {response.text}"
                    errors.append(error_msg)
            except Exception as e:
                error_msg = f"Batch {i//batch_size + 1} error: {str(e)}"
                errors.append(error_msg)

        return uploaded_count, errors, skipped_count

    def get_existing_ghl_record_keys(self):
        """Get existing GHL record keys for duplicate detection"""
        existing_records = self.get_existing_records_for_table(self.ghl_table_id, get_all=True)
        existing_keys = set()

        for record in existing_records:
            fields = record.get('fields', {})
            contact_id = fields.get('Contact ID')
            email = fields.get('email', '')
            phone = fields.get('phone', '')

            if contact_id:
                existing_keys.add(contact_id)
            elif email and phone:
                key = f"{email}_{phone}"
                existing_keys.add(key)

        return existing_keys

    def get_existing_records_for_table(self, table_id, date_range=None, get_all=False):
        """Get existing records from any Airtable table"""
        if not self.api_key:
            return []

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{table_id}"
            all_records = []
            offset = None

            while True:
                params = {}
                if not get_all:
                    params['maxRecords'] = 100

                if date_range:
                    # Add date filter if provided
                    start_date, end_date = date_range
                    filter_formula = f"AND(IS_AFTER({{Date}}, '{start_date}'), IS_BEFORE({{Date}}, '{end_date}'))"
                    params['filterByFormula'] = filter_formula

                if offset:
                    params['offset'] = offset

                response = requests.get(url, headers=self.headers, params=params)

                if response.status_code == 200:
                    data = response.json()
                    records = data.get('records', [])
                    all_records.extend(records)

                    # Check if there are more records
                    offset = data.get('offset')
                    if not offset or not get_all:
                        break
                else:
                    break

            return all_records
        except Exception:
            return []

    def get_existing_records(self, date_range=None, get_all=False):
        """Get existing records from Airtable"""
        if not self.api_key:
            return []

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"
            all_records = []
            offset = None

            while True:
                params = {}
                if not get_all:
                    params['maxRecords'] = 100

                if date_range:
                    # Add date filter if provided
                    start_date, end_date = date_range
                    filter_formula = f"AND(IS_AFTER({{Date}}, '{start_date}'), IS_BEFORE({{Date}}, '{end_date}'))"
                    params['filterByFormula'] = filter_formula

                if offset:
                    params['offset'] = offset

                response = requests.get(url, headers=self.headers, params=params)

                if response.status_code == 200:
                    data = response.json()
                    records = data.get('records', [])
                    all_records.extend(records)

                    # Check if there are more records
                    offset = data.get('offset')
                    if not offset or not get_all:
                        break
                else:
                    break

            return all_records
        except Exception:
            return []

    def get_existing_record_keys(self):
        """Get existing record keys (Date + Campaign ID combinations) for duplicate detection"""
        existing_records = self.get_existing_records(get_all=True)
        existing_keys = set()

        for record in existing_records:
            fields = record.get('fields', {})
            date = fields.get('Date')
            campaign_id = fields.get('Campaign ID')

            if date and campaign_id:
                key = f"{date}_{campaign_id}"
                existing_keys.add(key)

        return existing_keys

    def get_latest_date_in_airtable(self):
        """Get the most recent date in Airtable to determine where to start incremental update"""
        if not self.api_key:
            return None

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"
            params = {
                'maxRecords': 1,
                'sort[0][field]': 'Date',
                'sort[0][direction]': 'desc'
            }

            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                records = response.json().get('records', [])
                if records:
                    return records[0]['fields'].get('Date')

            return None
        except Exception:
            return None

    def delete_records(self, record_ids):
        """Delete records from Airtable"""
        if not self.api_key or not record_ids:
            return False

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"

            # Delete in batches of 10 (Airtable limit)
            for i in range(0, len(record_ids), 10):
                batch_ids = record_ids[i:i+10]
                params = {'records[]': batch_ids}
                response = requests.delete(url, headers=self.headers, params=params)

                if response.status_code != 200:
                    return False

            return True
        except Exception:
            return False

class ConfigManager:
    """Enhanced configuration manager with validation"""

    def __init__(self, config_file=CONFIG_FILE):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self):
        """Load configuration from file with validation"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    return self.validate_config(config)
            except Exception as e:
                print(f"Error loading config: {e}")
                return self.default_config()
        return self.default_config()

    def validate_config(self, config):
        """Validate configuration structure"""
        default = self.default_config()

        # Ensure all required keys exist
        for key in default:
            if key not in config:
                config[key] = default[key]

        # Validate credentials structure
        if "credentials" not in config or not isinstance(config["credentials"], dict):
            config["credentials"] = default["credentials"]

        for cred_key in default["credentials"]:
            if cred_key not in config["credentials"]:
                config["credentials"][cred_key] = default["credentials"][cred_key]

        return config

    def save_config(self):
        """Save configuration to file with backup"""
        try:
            # Create backup
            if os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup"
                with open(self.config_file, 'r') as src, open(backup_file, 'w') as dst:
                    dst.write(src.read())

            # Save new config
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False

    def default_config(self):
        """Return default configuration"""
        return {
            "credentials": {
                "customer_id": "",
                "developer_token": "",
                "refresh_token": "",
                "manager_account_id": "**********"
            },
            "airtable": {
                "api_key": "",
                "base_id": AIRTABLE_BASE_ID,
                "google_ads_table_id": AIRTABLE_GOOGLE_ADS_TABLE_ID,
                "auto_sync": False,
                "sync_mode": "incremental",
                "sync_on_extraction": True
            },
            "clients": [],
            "saved_sheets": [],
            "last_date_preset": "Last 30 days",
            "last_export_format": "csv",
            "scheduled_tasks": [],
            "window_geometry": "1400x900",
            "theme": "flatly",
            "auto_save": True,
            "show_tooltips": True
        }

class ModernGoogleAdsExtractor:
    """Modern Google Ads Data Extractor with enhanced UI"""

    def __init__(self, master):
        self.master = master
        self.config_manager = ConfigManager()

        # Initialize Airtable manager
        airtable_api_key = self.config_manager.config.get("airtable", {}).get("api_key", "")
        self.airtable_manager = AirtableManager(airtable_api_key)

        # Initialize theme
        self.theme = self.config_manager.config.get("theme", "flatly")
        self.style = ttk_bootstrap.Style(theme=self.theme)

        # Configure main window
        master.title("🚀 Google Ads Data Extractor - Professional Edition")
        master.geometry(self.config_manager.config.get("window_geometry", "1400x900"))
        master.minsize(1200, 700)

        # Set window icon (if available)
        try:
            master.iconbitmap("icon.ico")
        except:
            pass

        # Configure window closing
        master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Initialize variables
        self.ad_spend_data = None
        self.credentials = None
        self.scheduler_thread = None
        self.current_progress = None

        # Create UI
        self.setup_menu()
        self.setup_main_ui()
        self.setup_status_bar()

        # Load settings
        self.load_settings()

        # Start scheduler
        self.start_scheduler()

        # Log initial message
        self.log("🚀 Modern Google Ads Extractor started successfully!")
        self.log(f"📊 Theme: {self.theme.title()}")
        self.log(f"🔑 Client ID: {CLIENT_ID[:10]}... is pre-configured")
        self.log(f"🔐 Client Secret: {CLIENT_SECRET[:5]}... is pre-configured")

    def setup_menu(self):
        """Setup modern menu bar"""
        menubar = tk.Menu(self.master)
        self.master.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Configuration", command=self.new_config)
        file_menu.add_command(label="Open Configuration...", command=self.import_config)
        file_menu.add_command(label="Save Configuration", command=self.save_settings, accelerator="Ctrl+S")
        file_menu.add_command(label="Export Configuration...", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing, accelerator="Ctrl+Q")

        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)

        # Theme submenu
        theme_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="Theme", menu=theme_menu)

        themes = ["flatly", "darkly", "cosmo", "journal", "litera", "lumen", "minty", "pulse", "sandstone", "united", "yeti"]
        for theme in themes:
            theme_menu.add_command(label=theme.title(), command=lambda t=theme: self.change_theme(t))

        view_menu.add_separator()
        view_menu.add_command(label="Clear Log", command=self.clear_log)
        view_menu.add_command(label="Refresh Data", command=self.refresh_data)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Validate Credentials", command=self.validate_credentials)
        tools_menu.add_command(label="Test API Connection", command=self.test_api_connection)
        tools_menu.add_command(label="Data Preview", command=self.preview_data)
        tools_menu.add_separator()
        tools_menu.add_command(label="Bulk Export", command=self.bulk_export)
        tools_menu.add_command(label="Schedule Manager", command=self.open_schedule_manager)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="User Guide", command=self.show_help)
        help_menu.add_command(label="API Documentation", command=self.open_api_docs)
        help_menu.add_separator()
        help_menu.add_command(label="About", command=self.show_about)

        # Bind keyboard shortcuts
        self.master.bind('<Control-s>', lambda e: self.save_settings())
        self.master.bind('<Control-q>', lambda e: self.on_closing())
        self.master.bind('<F5>', lambda e: self.refresh_data())

    def setup_main_ui(self):
        """Setup the main user interface"""
        # Create main container with padding
        main_container = ttk_bootstrap.Frame(self.master, padding=10)
        main_container.pack(fill=BOTH, expand=True)

        # Create paned window for resizable layout
        paned_window = ttk_bootstrap.PanedWindow(main_container, orient=HORIZONTAL)
        paned_window.pack(fill=BOTH, expand=True)

        # Left panel for controls
        left_panel = ttk_bootstrap.Frame(paned_window, padding=10)
        paned_window.add(left_panel, weight=1)

        # Right panel for data and charts
        right_panel = ttk_bootstrap.Frame(paned_window, padding=10)
        paned_window.add(right_panel, weight=2)

        # Setup left panel
        self.setup_left_panel(left_panel)

        # Setup right panel
        self.setup_right_panel(right_panel)

    def setup_left_panel(self, parent):
        """Setup the left control panel"""
        # Header
        header_frame = ttk_bootstrap.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))

        title_label = ttk_bootstrap.Label(
            header_frame,
            text="Google Ads Data Extractor",
            font=('Arial', 16, 'bold'),
            bootstyle="primary"
        )
        title_label.pack()

        subtitle_label = ttk_bootstrap.Label(
            header_frame,
            text="Professional Edition",
            font=('Arial', 10),
            bootstyle="secondary"
        )
        subtitle_label.pack()

        # Create notebook for organized tabs
        self.notebook = ttk_bootstrap.Notebook(parent, bootstyle="primary")
        self.notebook.pack(fill=BOTH, expand=True)

        # Setup tabs
        self.setup_credentials_tab()
        self.setup_data_tab()
        self.setup_export_tab()
        self.setup_airtable_tab()
        self.setup_ghl_tab()
        self.setup_schedule_tab()
        self.setup_settings_tab()

    def setup_credentials_tab(self):
        """Setup credentials configuration tab"""
        creds_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(creds_frame, text="🔐 Credentials")

        # Credentials section
        creds_section = ttk_bootstrap.LabelFrame(creds_frame, text="Google Ads API Credentials", padding=15)
        creds_section.pack(fill=X, pady=(0, 15))

        # Customer ID
        ttk_bootstrap.Label(creds_section, text="Customer ID:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.customer_id_var = tk.StringVar()
        customer_id_entry = ttk_bootstrap.Entry(creds_section, textvariable=self.customer_id_var, width=30, font=('Arial', 10))
        customer_id_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(customer_id_entry, "Format: 123-456-7890")

        # Developer Token
        ttk_bootstrap.Label(creds_section, text="Developer Token:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        self.developer_token_var = tk.StringVar()
        dev_token_entry = ttk_bootstrap.Entry(creds_section, textvariable=self.developer_token_var, width=30, show="*", font=('Arial', 10))
        dev_token_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(dev_token_entry, "Your Google Ads API developer token")

        # Refresh Token
        ttk_bootstrap.Label(creds_section, text="Refresh Token:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='w', pady=5)
        self.refresh_token_var = tk.StringVar()
        refresh_token_entry = ttk_bootstrap.Entry(creds_section, textvariable=self.refresh_token_var, width=30, show="*", font=('Arial', 10))
        refresh_token_entry.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(refresh_token_entry, "OAuth refresh token for API access")

        # Manager Account ID
        ttk_bootstrap.Label(creds_section, text="Manager Account:", font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky='w', pady=5)
        self.manager_account_var = tk.StringVar(value="**********")
        manager_entry = ttk_bootstrap.Entry(creds_section, textvariable=self.manager_account_var, width=30, font=('Arial', 10))
        manager_entry.grid(row=3, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(manager_entry, "Your Google Ads manager account ID")

        # Configure grid weights
        creds_section.columnconfigure(1, weight=1)

        # OAuth button
        oauth_btn = ttk_bootstrap.Button(
            creds_section,
            text="🔑 Get OAuth Tokens",
            command=self.get_oauth_tokens,
            bootstyle="success-outline",
            width=20
        )
        oauth_btn.grid(row=4, column=0, columnspan=2, pady=15)

        # Validation button
        validate_btn = ttk_bootstrap.Button(
            creds_section,
            text="✓ Validate Credentials",
            command=self.validate_credentials,
            bootstyle="info-outline",
            width=20
        )
        validate_btn.grid(row=5, column=0, columnspan=2, pady=5)

    def setup_data_tab(self):
        """Setup data configuration tab"""
        data_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(data_frame, text="📊 Data")

        # Date range section
        date_section = ttk_bootstrap.LabelFrame(data_frame, text="Date Range Selection", padding=15)
        date_section.pack(fill=X, pady=(0, 15))

        # Date preset
        ttk_bootstrap.Label(date_section, text="Quick Select:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.date_preset_var = tk.StringVar()
        date_preset_combo = ttk_bootstrap.Combobox(
            date_section,
            textvariable=self.date_preset_var,
            values=list(DATE_PRESETS.keys()),
            state="readonly",
            width=20,
            font=('Arial', 10)
        )
        date_preset_combo.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        date_preset_combo.bind('<<ComboboxSelected>>', self.on_date_preset_change)

        # Start date
        ttk_bootstrap.Label(date_section, text="Start Date:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)

        # Set default start date (30 days ago)
        default_start = datetime.datetime.now() - datetime.timedelta(days=30)

        # Create frame for start date with calendar button
        start_date_frame = ttk_bootstrap.Frame(date_section)
        start_date_frame.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)
        start_date_frame.columnconfigure(0, weight=1)

        self.start_date_var = tk.StringVar(value=default_start.strftime('%Y-%m-%d'))
        self.start_date_entry = ttk_bootstrap.Entry(
            start_date_frame,
            textvariable=self.start_date_var,
            width=12,
            font=('Arial', 10)
        )
        self.start_date_entry.grid(row=0, column=0, sticky='ew', padx=(0, 5))

        start_cal_btn = ttk_bootstrap.Button(
            start_date_frame,
            text="📅",
            width=3,
            command=lambda: self.open_simple_calendar(self.start_date_var),
            bootstyle="info-outline"
        )
        start_cal_btn.grid(row=0, column=1)
        ModernTooltip(start_cal_btn, "Open calendar picker")

        # End date
        ttk_bootstrap.Label(date_section, text="End Date:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='w', pady=5)

        # Set default end date (today)
        default_end = datetime.datetime.now()

        # Create frame for end date with calendar button
        end_date_frame = ttk_bootstrap.Frame(date_section)
        end_date_frame.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)
        end_date_frame.columnconfigure(0, weight=1)

        self.end_date_var = tk.StringVar(value=default_end.strftime('%Y-%m-%d'))
        self.end_date_entry = ttk_bootstrap.Entry(
            end_date_frame,
            textvariable=self.end_date_var,
            width=12,
            font=('Arial', 10)
        )
        self.end_date_entry.grid(row=0, column=0, sticky='ew', padx=(0, 5))

        end_cal_btn = ttk_bootstrap.Button(
            end_date_frame,
            text="📅",
            width=3,
            command=lambda: self.open_simple_calendar(self.end_date_var),
            bootstyle="info-outline"
        )
        end_cal_btn.grid(row=0, column=1)
        ModernTooltip(end_cal_btn, "Open calendar picker")

        date_section.columnconfigure(1, weight=1)

        # Client management section
        client_section = ttk_bootstrap.LabelFrame(data_frame, text="Client Management", padding=15)
        client_section.pack(fill=X, pady=(0, 15))

        # Client selection
        ttk_bootstrap.Label(client_section, text="Select Client:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.client_var = tk.StringVar()
        self.client_combo = ttk_bootstrap.Combobox(
            client_section,
            textvariable=self.client_var,
            state="readonly",
            width=30,
            font=('Arial', 10)
        )
        self.client_combo.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        self.client_combo.bind('<<ComboboxSelected>>', self.on_client_select)

        # Client management buttons
        client_btn_frame = ttk_bootstrap.Frame(client_section)
        client_btn_frame.grid(row=1, column=0, columnspan=2, pady=10)

        ttk_bootstrap.Button(
            client_btn_frame,
            text="➕ Add Client",
            command=self.add_client_dialog,
            bootstyle="success-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            client_btn_frame,
            text="✏️ Edit Client",
            command=self.edit_client_dialog,
            bootstyle="warning-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            client_btn_frame,
            text="🗑️ Remove Client",
            command=self.remove_client_dialog,
            bootstyle="danger-outline"
        ).pack(side=LEFT, padx=5)

        client_section.columnconfigure(1, weight=1)

        # Action buttons
        action_frame = ttk_bootstrap.Frame(data_frame)
        action_frame.pack(fill=X, pady=15)

        extract_btn = ttk_bootstrap.Button(
            action_frame,
            text="🚀 Extract Data",
            command=self.start_extraction,
            bootstyle="primary",
            width=20
        )
        extract_btn.pack(side=LEFT, padx=5)

        load_btn = ttk_bootstrap.Button(
            action_frame,
            text="📂 Load CSV Data",
            command=self.load_csv_data,
            bootstyle="warning",
            width=20
        )
        load_btn.pack(side=LEFT, padx=5)

        preview_btn = ttk_bootstrap.Button(
            action_frame,
            text="👁️ Preview Data",
            command=self.preview_data,
            bootstyle="info-outline",
            width=20
        )
        preview_btn.pack(side=LEFT, padx=5)

    def setup_export_tab(self):
        """Setup export configuration tab"""
        export_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(export_frame, text="📤 Export")

        # Export format section
        format_section = ttk_bootstrap.LabelFrame(export_frame, text="Export Format", padding=15)
        format_section.pack(fill=X, pady=(0, 15))

        self.export_format_var = tk.StringVar(value="csv")

        # Format options with icons
        formats = [
            ("📄 CSV", "csv", "Comma-separated values file"),
            ("📊 Excel", "excel", "Microsoft Excel spreadsheet"),
            ("🌐 Google Sheets", "sheets", "Export directly to Google Sheets")
        ]

        for i, (text, value, tooltip) in enumerate(formats):
            radio = ttk_bootstrap.Radiobutton(
                format_section,
                text=text,
                variable=self.export_format_var,
                value=value,
                bootstyle="primary"
            )
            radio.grid(row=i, column=0, sticky='w', pady=5)
            ModernTooltip(radio, tooltip)

        # Google Sheets section
        sheets_section = ttk_bootstrap.LabelFrame(export_frame, text="Google Sheets Options", padding=15)
        sheets_section.pack(fill=X, pady=(0, 15))

        # Sheet ID
        ttk_bootstrap.Label(sheets_section, text="Sheet ID:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.sheet_id_var = tk.StringVar()
        sheet_id_entry = ttk_bootstrap.Entry(sheets_section, textvariable=self.sheet_id_var, width=40, font=('Arial', 10))
        sheet_id_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(sheet_id_entry, "Leave blank to create a new spreadsheet")

        # Sheet name
        ttk_bootstrap.Label(sheets_section, text="Sheet Name:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        self.sheet_name_var = tk.StringVar(value="Ad Spend Data")
        sheet_name_entry = ttk_bootstrap.Entry(sheets_section, textvariable=self.sheet_name_var, width=40, font=('Arial', 10))
        sheet_name_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)

        sheets_section.columnconfigure(1, weight=1)

        # Export buttons
        export_btn_frame = ttk_bootstrap.Frame(export_frame)
        export_btn_frame.pack(fill=X, pady=15)

        export_btn = ttk_bootstrap.Button(
            export_btn_frame,
            text="📤 Export Data",
            command=self.export_data,
            bootstyle="success",
            width=20
        )
        export_btn.pack(side=LEFT, padx=5)

        bulk_export_btn = ttk_bootstrap.Button(
            export_btn_frame,
            text="📦 Bulk Export",
            command=self.bulk_export,
            bootstyle="warning-outline",
            width=20
        )
        bulk_export_btn.pack(side=LEFT, padx=5)

    def setup_airtable_tab(self):
        """Setup Airtable integration tab"""
        airtable_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(airtable_frame, text="🗃️ Airtable")

        # Airtable info
        info_label = ttk_bootstrap.Label(
            airtable_frame,
            text="Sync Google Ads data directly to your Airtable workspace",
            font=('Arial', 11),
            bootstyle="info"
        )
        info_label.pack(pady=(0, 15))

        # API Configuration section
        api_section = ttk_bootstrap.LabelFrame(airtable_frame, text="API Configuration", padding=15)
        api_section.pack(fill=X, pady=(0, 15))

        # API Key
        ttk_bootstrap.Label(api_section, text="Airtable API Key:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.airtable_api_key_var = tk.StringVar()
        api_key_entry = ttk_bootstrap.Entry(api_section, textvariable=self.airtable_api_key_var, width=40, show="*", font=('Arial', 10))
        api_key_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(api_key_entry, "Your Airtable Personal Access Token")

        # Base ID (read-only, pre-configured)
        ttk_bootstrap.Label(api_section, text="Base ID:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        self.airtable_base_id_var = tk.StringVar(value=AIRTABLE_BASE_ID)
        base_id_entry = ttk_bootstrap.Entry(api_section, textvariable=self.airtable_base_id_var, width=40, state="readonly", font=('Arial', 10))
        base_id_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(base_id_entry, "QuickFix Airtable Base ID (pre-configured)")

        # Table ID (read-only, pre-configured)
        ttk_bootstrap.Label(api_section, text="Table ID:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='w', pady=5)
        self.airtable_table_id_var = tk.StringVar(value=AIRTABLE_GOOGLE_ADS_TABLE_ID)
        table_id_entry = ttk_bootstrap.Entry(api_section, textvariable=self.airtable_table_id_var, width=40, state="readonly", font=('Arial', 10))
        table_id_entry.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(table_id_entry, "Google Ads Table ID (pre-configured)")

        api_section.columnconfigure(1, weight=1)

        # Test connection button
        test_btn = ttk_bootstrap.Button(
            api_section,
            text="🔗 Test Connection",
            command=self.test_airtable_connection,
            bootstyle="info-outline",
            width=20
        )
        test_btn.grid(row=3, column=0, columnspan=2, pady=15)

        # Sync Options section
        sync_section = ttk_bootstrap.LabelFrame(airtable_frame, text="Sync Options", padding=15)
        sync_section.pack(fill=X, pady=(0, 15))

        # Sync mode selection
        ttk_bootstrap.Label(sync_section, text="Sync Mode:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.airtable_sync_mode_var = tk.StringVar(value="incremental")

        sync_modes = [
            ("🔄 Incremental (Add only new records)", "incremental"),
            ("➕ Append (Add all records)", "append"),
            ("🔄 Replace (Clear table first)", "replace")
        ]

        for i, (text, value) in enumerate(sync_modes):
            radio = ttk_bootstrap.Radiobutton(
                sync_section,
                text=text,
                variable=self.airtable_sync_mode_var,
                value=value,
                bootstyle="primary"
            )
            radio.grid(row=i+1, column=0, sticky='w', pady=2, padx=20)

        # Mode descriptions
        mode_info = ttk_bootstrap.Label(
            sync_section,
            text="💡 Incremental mode automatically detects and skips existing records",
            font=('Arial', 9),
            bootstyle="info"
        )
        mode_info.grid(row=4, column=0, sticky='w', pady=(10, 5))

        # Auto-sync option
        self.airtable_auto_sync_var = tk.BooleanVar()
        auto_sync_check = ttk_bootstrap.Checkbutton(
            sync_section,
            text="Auto-sync after data extraction",
            variable=self.airtable_auto_sync_var,
            bootstyle="primary"
        )
        auto_sync_check.grid(row=5, column=0, sticky='w', pady=5)

        # Sync Actions section
        actions_section = ttk_bootstrap.LabelFrame(airtable_frame, text="Sync Actions", padding=15)
        actions_section.pack(fill=X, pady=(0, 15))

        # Action buttons
        sync_btn_frame = ttk_bootstrap.Frame(actions_section)
        sync_btn_frame.pack(fill=X)

        ttk_bootstrap.Button(
            sync_btn_frame,
            text="🔄 Sync Current Data",
            command=self.sync_to_airtable,
            bootstyle="success",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            sync_btn_frame,
            text="📊 View Airtable Data",
            command=self.view_airtable_data,
            bootstyle="info-outline",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            sync_btn_frame,
            text="🗑️ Clear Airtable Data",
            command=self.clear_airtable_data,
            bootstyle="danger-outline",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            sync_btn_frame,
            text="📊 Show Statistics",
            command=self.show_airtable_stats,
            bootstyle="secondary-outline",
            width=20
        ).pack(side=LEFT, padx=5)

        # Status section
        status_section = ttk_bootstrap.LabelFrame(airtable_frame, text="Sync Status", padding=15)
        status_section.pack(fill=BOTH, expand=True)

        # Status text area
        self.airtable_status_text = scrolledtext.ScrolledText(
            status_section,
            height=8,
            font=('Consolas', 9),
            wrap=tk.WORD
        )
        self.airtable_status_text.pack(fill=BOTH, expand=True)

    def setup_ghl_tab(self):
        """Setup GHL (GoHighLevel) data import tab"""
        ghl_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(ghl_frame, text="🎯 GHL")

        # GHL info
        info_label = ttk_bootstrap.Label(
            ghl_frame,
            text="Import and sync GoHighLevel lead data to your Airtable workspace",
            font=('Arial', 11),
            bootstyle="info"
        )
        info_label.pack(pady=(0, 15))

        # File Import section
        import_section = ttk_bootstrap.LabelFrame(ghl_frame, text="CSV File Import", padding=15)
        import_section.pack(fill=X, pady=(0, 15))

        # File selection
        file_frame = ttk_bootstrap.Frame(import_section)
        file_frame.pack(fill=X, pady=5)

        ttk_bootstrap.Label(file_frame, text="GHL CSV File:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.ghl_file_var = tk.StringVar()
        file_entry = ttk_bootstrap.Entry(file_frame, textvariable=self.ghl_file_var, width=50, font=('Arial', 10))
        file_entry.pack(side=LEFT, padx=(10, 5), fill=X, expand=True)

        ttk_bootstrap.Button(
            file_frame,
            text="📁 Browse",
            command=self.browse_ghl_file,
            bootstyle="secondary-outline",
            width=10
        ).pack(side=RIGHT)

        # Quick load button for existing file
        quick_load_frame = ttk_bootstrap.Frame(import_section)
        quick_load_frame.pack(fill=X, pady=5)

        ttk_bootstrap.Button(
            quick_load_frame,
            text="⚡ Load quickfix - ghl.csv",
            command=self.load_default_ghl_file,
            bootstyle="info-outline",
            width=25
        ).pack(side=LEFT)

        # Data preview section
        preview_section = ttk_bootstrap.LabelFrame(ghl_frame, text="Data Preview", padding=15)
        preview_section.pack(fill=BOTH, expand=True, pady=(0, 15))

        # Preview controls
        preview_controls = ttk_bootstrap.Frame(preview_section)
        preview_controls.pack(fill=X, pady=(0, 10))

        ttk_bootstrap.Button(
            preview_controls,
            text="👁️ Preview Data",
            command=self.preview_ghl_data,
            bootstyle="info",
            width=15
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            preview_controls,
            text="🔄 Sync to Airtable",
            command=self.sync_ghl_to_airtable,
            bootstyle="success",
            width=15
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            preview_controls,
            text="📊 View GHL Table",
            command=self.view_ghl_airtable,
            bootstyle="secondary-outline",
            width=15
        ).pack(side=LEFT, padx=5)

        # Data preview area
        preview_frame = ttk_bootstrap.Frame(preview_section)
        preview_frame.pack(fill=BOTH, expand=True)

        # Create treeview for data preview
        columns = ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created']
        self.ghl_tree = ttk.Treeview(preview_frame, columns=columns, show='headings', height=10)

        # Configure columns
        for col in columns:
            self.ghl_tree.heading(col, text=col)
            self.ghl_tree.column(col, width=120, minwidth=80)

        # Scrollbars
        ghl_v_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.ghl_tree.yview)
        ghl_h_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, command=self.ghl_tree.xview)
        self.ghl_tree.configure(yscrollcommand=ghl_v_scrollbar.set, xscrollcommand=ghl_h_scrollbar.set)

        # Pack treeview and scrollbars
        self.ghl_tree.pack(side=LEFT, fill=BOTH, expand=True)
        ghl_v_scrollbar.pack(side=RIGHT, fill=Y)
        ghl_h_scrollbar.pack(side=BOTTOM, fill=X)

        # Status section
        ghl_status_section = ttk_bootstrap.LabelFrame(ghl_frame, text="Import Status", padding=15)
        ghl_status_section.pack(fill=X)

        # Status text area
        self.ghl_status_text = scrolledtext.ScrolledText(
            ghl_status_section,
            height=6,
            font=('Consolas', 9),
            wrap=tk.WORD
        )
        self.ghl_status_text.pack(fill=BOTH, expand=True)

        # Initialize with empty data
        self.ghl_data = None

    def setup_schedule_tab(self):
        """Setup scheduling tab"""
        schedule_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(schedule_frame, text="⏰ Schedule")

        # Schedule info
        info_label = ttk_bootstrap.Label(
            schedule_frame,
            text="Automated data extraction and export scheduling",
            font=('Arial', 11),
            bootstyle="info"
        )
        info_label.pack(pady=(0, 15))

        # Schedule list
        schedule_list_frame = ttk_bootstrap.LabelFrame(schedule_frame, text="Scheduled Tasks", padding=15)
        schedule_list_frame.pack(fill=BOTH, expand=True, pady=(0, 15))

        # Treeview for schedules
        columns = ('Type', 'Time', 'Format', 'Status', 'Last Run')
        self.schedule_tree = ttk_bootstrap.Treeview(
            schedule_list_frame,
            columns=columns,
            show='tree headings',
            height=8
        )

        # Configure columns
        self.schedule_tree.heading('#0', text='ID')
        self.schedule_tree.column('#0', width=50)

        for col in columns:
            self.schedule_tree.heading(col, text=col)
            self.schedule_tree.column(col, width=100)

        # Scrollbar for treeview
        schedule_scrollbar = ttk_bootstrap.Scrollbar(schedule_list_frame, orient=VERTICAL, command=self.schedule_tree.yview)
        self.schedule_tree.configure(yscrollcommand=schedule_scrollbar.set)

        self.schedule_tree.pack(side=LEFT, fill=BOTH, expand=True)
        schedule_scrollbar.pack(side=RIGHT, fill=Y)

        # Schedule buttons
        schedule_btn_frame = ttk_bootstrap.Frame(schedule_frame)
        schedule_btn_frame.pack(fill=X)

        ttk_bootstrap.Button(
            schedule_btn_frame,
            text="➕ Add Schedule",
            command=self.add_schedule_dialog,
            bootstyle="success-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            schedule_btn_frame,
            text="✏️ Edit Schedule",
            command=self.edit_schedule_dialog,
            bootstyle="warning-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            schedule_btn_frame,
            text="🗑️ Remove Schedule",
            command=self.remove_schedule_dialog,
            bootstyle="danger-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            schedule_btn_frame,
            text="▶️ Run Now",
            command=self.run_schedule_now,
            bootstyle="info-outline"
        ).pack(side=LEFT, padx=5)

    def setup_settings_tab(self):
        """Setup settings tab"""
        settings_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(settings_frame, text="⚙️ Settings")

        # Theme section
        theme_section = ttk_bootstrap.LabelFrame(settings_frame, text="Appearance", padding=15)
        theme_section.pack(fill=X, pady=(0, 15))

        ttk_bootstrap.Label(theme_section, text="Theme:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.theme_var = tk.StringVar(value=self.theme)
        theme_combo = ttk_bootstrap.Combobox(
            theme_section,
            textvariable=self.theme_var,
            values=["flatly", "darkly", "cosmo", "journal", "litera", "lumen", "minty", "pulse", "sandstone", "united", "yeti"],
            state="readonly",
            width=20
        )
        theme_combo.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        theme_combo.bind('<<ComboboxSelected>>', self.on_theme_change)

        theme_section.columnconfigure(1, weight=1)

        # Configuration section
        config_section = ttk_bootstrap.LabelFrame(settings_frame, text="Configuration", padding=15)
        config_section.pack(fill=X, pady=(0, 15))

        # Auto-save option
        self.auto_save_var = tk.BooleanVar(value=self.config_manager.config.get("auto_save", True))
        auto_save_check = ttk_bootstrap.Checkbutton(
            config_section,
            text="Auto-save settings",
            variable=self.auto_save_var,
            bootstyle="primary"
        )
        auto_save_check.grid(row=0, column=0, sticky='w', pady=5)

        # Show tooltips option
        self.show_tooltips_var = tk.BooleanVar(value=self.config_manager.config.get("show_tooltips", True))
        tooltips_check = ttk_bootstrap.Checkbutton(
            config_section,
            text="Show tooltips",
            variable=self.show_tooltips_var,
            bootstyle="primary"
        )
        tooltips_check.grid(row=1, column=0, sticky='w', pady=5)

        # Configuration buttons
        config_btn_frame = ttk_bootstrap.Frame(settings_frame)
        config_btn_frame.pack(fill=X, pady=15)

        ttk_bootstrap.Button(
            config_btn_frame,
            text="💾 Save Settings",
            command=self.save_settings,
            bootstyle="success"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            config_btn_frame,
            text="📁 Export Config",
            command=self.export_config,
            bootstyle="info-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            config_btn_frame,
            text="📂 Import Config",
            command=self.import_config,
            bootstyle="warning-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            config_btn_frame,
            text="🔄 Reset to Defaults",
            command=self.reset_config,
            bootstyle="danger-outline"
        ).pack(side=LEFT, padx=5)

    def setup_right_panel(self, parent):
        """Setup the right panel with dashboard and data view"""
        # Create notebook for right panel tabs
        right_notebook = ttk_bootstrap.Notebook(parent, bootstyle="info")
        right_notebook.pack(fill=BOTH, expand=True)

        # Dashboard tab
        self.setup_dashboard_tab(right_notebook)

        # Data view tab
        self.setup_data_view_tab(right_notebook)

        # Log tab
        self.setup_log_tab(right_notebook)

    def setup_dashboard_tab(self, parent):
        """Setup dashboard with KPIs"""
        dashboard_frame = ttk_bootstrap.Frame(parent, padding=15)
        parent.add(dashboard_frame, text="📊 Dashboard")

        # KPI Cards container
        kpi_container = ttk_bootstrap.Frame(dashboard_frame)
        kpi_container.pack(fill=X, pady=(0, 20))

        # Create KPI cards
        self.kpi_cards = {}

        # Total Cost KPI
        self.kpi_cards['cost'] = ModernKPICard(
            kpi_container,
            "Total Cost",
            "$0.00",
            color="primary"
        )
        self.kpi_cards['cost'].pack(side=LEFT, fill=X, expand=True, padx=5)

        # Total Clicks KPI
        self.kpi_cards['clicks'] = ModernKPICard(
            kpi_container,
            "Total Clicks",
            "0",
            color="success"
        )
        self.kpi_cards['clicks'].pack(side=LEFT, fill=X, expand=True, padx=5)

        # Total Impressions KPI
        self.kpi_cards['impressions'] = ModernKPICard(
            kpi_container,
            "Total Impressions",
            "0",
            color="info"
        )
        self.kpi_cards['impressions'].pack(side=LEFT, fill=X, expand=True, padx=5)

        # Conversion Rate KPI
        self.kpi_cards['conv_rate'] = ModernKPICard(
            kpi_container,
            "Conv. Rate",
            "0.00%",
            color="warning"
        )
        self.kpi_cards['conv_rate'].pack(side=LEFT, fill=X, expand=True, padx=5)

        # Quick actions section
        actions_section = ttk_bootstrap.LabelFrame(dashboard_frame, text="Quick Actions", padding=15)
        actions_section.pack(fill=X, pady=(0, 20))

        # Quick action buttons
        quick_actions = [
            ("🚀 Quick Extract", self.quick_extract, "primary"),
            ("📊 Generate Report", self.generate_report, "success"),
            ("📤 Quick Export", self.quick_export, "info"),
            ("🔄 Refresh Data", self.refresh_data, "warning")
        ]

        for i, (text, command, style) in enumerate(quick_actions):
            btn = ttk_bootstrap.Button(
                actions_section,
                text=text,
                command=command,
                bootstyle=f"{style}-outline",
                width=15
            )
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky='ew')

        actions_section.columnconfigure(0, weight=1)
        actions_section.columnconfigure(1, weight=1)

        # Recent activity section
        activity_section = ttk_bootstrap.LabelFrame(dashboard_frame, text="Recent Activity", padding=15)
        activity_section.pack(fill=BOTH, expand=True)

        # Activity listbox
        self.activity_listbox = tk.Listbox(
            activity_section,
            height=8,
            font=('Arial', 9)
        )
        self.activity_listbox.pack(fill=BOTH, expand=True)

        # Add some sample activities
        self.add_activity("Application started")
        self.add_activity("Configuration loaded")

    def setup_data_view_tab(self, parent):
        """Setup data view tab with functional table"""
        data_frame = ttk_bootstrap.Frame(parent, padding=15)
        parent.add(data_frame, text="📋 Data View")

        # Data controls frame
        controls_frame = ttk_bootstrap.Frame(data_frame)
        controls_frame.pack(fill=X, pady=(0, 10))

        # Data action buttons
        ttk_bootstrap.Button(
            controls_frame,
            text="🔄 Refresh View",
            command=self.refresh_data_view,
            bootstyle="info-outline",
            width=15
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            controls_frame,
            text="👁️ Preview Data",
            command=self.preview_data,
            bootstyle="primary-outline",
            width=15
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            controls_frame,
            text="📤 Export Data",
            command=self.export_data,
            bootstyle="success-outline",
            width=15
        ).pack(side=LEFT, padx=5)

        # Data table frame
        table_frame = ttk_bootstrap.Frame(data_frame)
        table_frame.pack(fill=BOTH, expand=True)

        # Create treeview for data display
        columns = ('Date', 'Campaign', 'Cost', 'Clicks', 'Impressions', 'Conversions')
        self.data_tree = ttk_bootstrap.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=12,
            bootstyle="primary"
        )

        # Configure columns
        column_widths = {'Date': 100, 'Campaign': 250, 'Cost': 100, 'Clicks': 80,
                        'Impressions': 120, 'Conversions': 100}

        for col in columns:
            self.data_tree.heading(col, text=col, anchor='center')
            self.data_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # Scrollbars
        v_scrollbar = ttk_bootstrap.Scrollbar(table_frame, orient=VERTICAL, command=self.data_tree.yview)
        h_scrollbar = ttk_bootstrap.Scrollbar(table_frame, orient=HORIZONTAL, command=self.data_tree.xview)

        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout for better control
        self.data_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # Data summary frame
        summary_frame = ttk_bootstrap.Frame(data_frame)
        summary_frame.pack(fill=X, pady=(10, 0))

        self.data_summary_label = ttk_bootstrap.Label(
            summary_frame,
            text="No data loaded - Use 'Extract Data' or 'Load CSV Data' to populate this view",
            font=('Arial', 10),
            bootstyle="secondary"
        )
        self.data_summary_label.pack()

        # Initialize with empty data
        self.refresh_data_view()

    def refresh_data_view(self):
        """Refresh the data view tab with current data"""
        try:
            # Clear existing data
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)

            # Check if we have data to display
            if hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None and not self.ad_spend_data.empty:
                # Get the first 50 rows for display (to avoid performance issues)
                display_data = self.ad_spend_data.head(50)

                for index, row in display_data.iterrows():
                    # Format the data for display
                    values = []

                    # Date
                    values.append(str(row.get('Date', '')))

                    # Campaign Name
                    campaign_name = str(row.get('Campaign Name', ''))
                    if len(campaign_name) > 30:
                        campaign_name = campaign_name[:27] + "..."
                    values.append(campaign_name)

                    # Cost
                    cost = row.get('Cost', 0)
                    values.append(f"${cost:.2f}")

                    # Clicks
                    clicks = row.get('Clicks', 0)
                    values.append(f"{int(clicks):,}")

                    # Impressions
                    impressions = row.get('Impressions', 0)
                    values.append(f"{int(impressions):,}")

                    # Conversions
                    conversions = row.get('Conversions', 0)
                    values.append(f"{conversions:.2f}")

                    self.data_tree.insert('', 'end', values=values)

                # Update summary
                total_records = len(self.ad_spend_data)
                total_cost = self.ad_spend_data['Cost'].sum() if 'Cost' in self.ad_spend_data.columns else 0

                if total_records > 50:
                    summary_text = f"Showing first 50 of {total_records:,} records | Total Cost: ${total_cost:,.2f}"
                else:
                    summary_text = f"Showing {total_records:,} records | Total Cost: ${total_cost:,.2f}"

                self.data_summary_label.config(text=summary_text)

            else:
                # No data available
                self.data_summary_label.config(text="No data loaded - Use 'Extract Data' or 'Load CSV Data' to populate this view")

        except Exception as e:
            self.log(f"❌ Error refreshing data view: {str(e)}")
            self.data_summary_label.config(text="Error loading data view")

    def setup_log_tab(self, parent):
        """Setup log tab"""
        log_frame = ttk_bootstrap.Frame(parent, padding=15)
        parent.add(log_frame, text="📝 Log")

        # Log controls
        log_controls = ttk_bootstrap.Frame(log_frame)
        log_controls.pack(fill=X, pady=(0, 10))

        ttk_bootstrap.Button(
            log_controls,
            text="🗑️ Clear Log",
            command=self.clear_log,
            bootstyle="danger-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            log_controls,
            text="💾 Save Log",
            command=self.save_log,
            bootstyle="info-outline"
        ).pack(side=LEFT, padx=5)

        # Log text area
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=20,
            font=('Consolas', 9),
            wrap=tk.WORD
        )
        self.log_text.pack(fill=BOTH, expand=True)

    def setup_status_bar(self):
        """Setup modern status bar"""
        status_frame = ttk_bootstrap.Frame(self.master)
        status_frame.pack(side=BOTTOM, fill=X)

        # Status label
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk_bootstrap.Label(
            status_frame,
            textvariable=self.status_var,
            font=('Arial', 9),
            padding=5
        )
        status_label.pack(side=LEFT)

        # Progress bar (hidden by default)
        self.status_progress = ttk_bootstrap.Progressbar(
            status_frame,
            mode='indeterminate',
            bootstyle="info-striped",
            length=200
        )

        # Connection status
        self.connection_status = ttk_bootstrap.Label(
            status_frame,
            text="🔴 Disconnected",
            font=('Arial', 9),
            padding=5
        )
        self.connection_status.pack(side=RIGHT)

        # Theme indicator
        theme_label = ttk_bootstrap.Label(
            status_frame,
            text=f"Theme: {self.theme.title()}",
            font=('Arial', 9),
            padding=5
        )
        theme_label.pack(side=RIGHT)

    # Core functionality methods (simplified for demo)
    def log(self, message):
        """Add message to log with timestamp"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        # Add to log text widget
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, formatted_message + "\n")
            self.log_text.see(tk.END)

        # Add to activity list
        self.add_activity(message)

        # Update status
        self.status_var.set(message)

        print(formatted_message)  # Also print to console

    def add_activity(self, activity):
        """Add activity to recent activity list"""
        if hasattr(self, 'activity_listbox'):
            timestamp = datetime.datetime.now().strftime("%H:%M")
            self.activity_listbox.insert(0, f"{timestamp} - {activity}")

            # Keep only last 20 activities
            if self.activity_listbox.size() > 20:
                self.activity_listbox.delete(20, tk.END)

    def clear_log(self):
        """Clear the log"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
        self.log("Log cleared")

    def save_log(self):
        """Save log to file"""
        if hasattr(self, 'log_text'):
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                try:
                    with open(filename, 'w') as f:
                        f.write(self.log_text.get(1.0, tk.END))
                    self.log(f"Log saved to {filename}")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to save log: {str(e)}")

    def change_theme(self, theme):
        """Change application theme"""
        try:
            self.theme = theme
            self.style = ttk_bootstrap.Style(theme=theme)
            self.config_manager.config["theme"] = theme
            if self.auto_save_var.get():
                self.config_manager.save_config()
            self.log(f"Theme changed to {theme.title()}")
        except Exception as e:
            messagebox.showerror("Theme Error", f"Failed to change theme: {str(e)}")

    def on_theme_change(self, event=None):
        """Handle theme change from combobox"""
        self.change_theme(self.theme_var.get())

    def on_date_preset_change(self, event=None):
        """Handle date preset change"""
        preset = self.date_preset_var.get()
        if preset in DATE_PRESETS and preset != "Custom":
            start_date, end_date = DATE_PRESETS[preset]()
            if start_date and end_date:
                self.start_date_var.set(start_date)
                self.end_date_var.set(end_date)
                self.log(f"Date range set to: {preset} ({start_date} to {end_date})")

    def on_client_select(self, event=None):
        """Handle client selection"""
        client_name = self.client_var.get()
        if client_name:
            # Find client in config
            for client in self.config_manager.config.get("clients", []):
                if client.get("client_name") == client_name:
                    self.customer_id_var.set(client.get("customer_id_formatted", ""))
                    self.log(f"Selected client: {client_name}")
                    break

    def load_settings(self):
        """Load settings from configuration"""
        config = self.config_manager.config

        # Load credentials
        self.customer_id_var.set(config["credentials"].get("customer_id", ""))
        self.developer_token_var.set(config["credentials"].get("developer_token", ""))
        self.refresh_token_var.set(config["credentials"].get("refresh_token", ""))
        self.manager_account_var.set(config["credentials"].get("manager_account_id", "**********"))

        # Load Airtable settings
        airtable_config = config.get("airtable", {})
        if hasattr(self, 'airtable_api_key_var'):
            self.airtable_api_key_var.set(airtable_config.get("api_key", ""))
        if hasattr(self, 'airtable_auto_sync_var'):
            self.airtable_auto_sync_var.set(airtable_config.get("auto_sync", False))
        if hasattr(self, 'airtable_sync_mode_var'):
            self.airtable_sync_mode_var.set(airtable_config.get("sync_mode", "incremental"))

        # Load other settings
        self.date_preset_var.set(config.get("last_date_preset", "Last 30 days"))
        self.export_format_var.set(config.get("last_export_format", "csv"))

        # Load clients
        self.update_client_list()

        self.log("Settings loaded successfully")

    def save_settings(self):
        """Save current settings"""
        try:
            config = self.config_manager.config

            # Save credentials
            config["credentials"]["customer_id"] = self.customer_id_var.get()
            config["credentials"]["developer_token"] = self.developer_token_var.get()
            config["credentials"]["refresh_token"] = self.refresh_token_var.get()
            config["credentials"]["manager_account_id"] = self.manager_account_var.get()

            # Save Airtable settings
            if hasattr(self, 'airtable_api_key_var'):
                config["airtable"]["api_key"] = self.airtable_api_key_var.get()
            if hasattr(self, 'airtable_auto_sync_var'):
                config["airtable"]["auto_sync"] = self.airtable_auto_sync_var.get()
            if hasattr(self, 'airtable_sync_mode_var'):
                config["airtable"]["sync_mode"] = self.airtable_sync_mode_var.get()

            # Save other settings
            config["last_date_preset"] = self.date_preset_var.get()
            config["last_export_format"] = self.export_format_var.get()
            config["theme"] = self.theme
            config["auto_save"] = self.auto_save_var.get()
            config["show_tooltips"] = self.show_tooltips_var.get()

            # Save window geometry
            config["window_geometry"] = self.master.geometry()

            # Save to file
            if self.config_manager.save_config():
                self.log("Settings saved successfully")
                messagebox.showinfo("Success", "Settings saved successfully!")
            else:
                messagebox.showerror("Error", "Failed to save settings")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def update_client_list(self):
        """Update client combobox"""
        clients = self.config_manager.config.get("clients", [])
        client_names = [client.get("client_name", "") for client in clients]

        if hasattr(self, 'client_combo'):
            self.client_combo['values'] = client_names
            if client_names and not self.client_var.get():
                self.client_var.set(client_names[0])

    def open_simple_calendar(self, date_var):
        """Open a simple date picker dialog"""
        try:
            # Parse current date
            current_date_str = date_var.get()
            try:
                current_date = datetime.datetime.strptime(current_date_str, '%Y-%m-%d')
            except:
                current_date = datetime.datetime.now()

            # Create date picker dialog
            dialog = tk.Toplevel(self.master)
            dialog.title("📅 Select Date")
            dialog.geometry("350x200")
            dialog.resizable(False, False)
            dialog.transient(self.master)
            dialog.grab_set()

            # Center the dialog
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (350 // 2)
            y = (dialog.winfo_screenheight() // 2) - (200 // 2)
            dialog.geometry(f"350x200+{x}+{y}")

            result = [None]  # Use list to store result for closure

            # Main frame
            main_frame = tk.Frame(dialog, padx=20, pady=20)
            main_frame.pack(fill='both', expand=True)

            # Date selection frame
            date_frame = tk.Frame(main_frame)
            date_frame.pack(pady=10)

            # Year selection
            tk.Label(date_frame, text="Year:", font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=5, pady=5)
            year_var = tk.StringVar(value=str(current_date.year))
            year_spinbox = tk.Spinbox(date_frame, from_=2020, to=2030, textvariable=year_var, width=8, font=('Arial', 10))
            year_spinbox.grid(row=0, column=1, padx=5, pady=5)

            # Month selection
            tk.Label(date_frame, text="Month:", font=('Arial', 10, 'bold')).grid(row=0, column=2, padx=5, pady=5)
            months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
            month_var = tk.StringVar(value=f"{current_date.month:02d}")
            month_combo = ttk.Combobox(date_frame, textvariable=month_var, values=months, width=6, font=('Arial', 10))
            month_combo.grid(row=0, column=3, padx=5, pady=5)

            # Day selection
            tk.Label(date_frame, text="Day:", font=('Arial', 10, 'bold')).grid(row=1, column=0, padx=5, pady=5)
            day_var = tk.StringVar(value=f"{current_date.day:02d}")
            day_spinbox = tk.Spinbox(date_frame, from_=1, to=31, textvariable=day_var, width=8, font=('Arial', 10))
            day_spinbox.grid(row=1, column=1, padx=5, pady=5)

            # Current date display
            current_label = tk.Label(date_frame, text=f"Current: {current_date.strftime('%Y-%m-%d')}",
                                   font=('Arial', 9), fg='blue')
            current_label.grid(row=1, column=2, columnspan=2, padx=5, pady=5)

            # Button frame
            btn_frame = tk.Frame(main_frame)
            btn_frame.pack(pady=20)

            def on_ok():
                try:
                    year = int(year_var.get())
                    month = int(month_var.get())
                    day = int(day_var.get())
                    selected_date = datetime.date(year, month, day)
                    result[0] = selected_date.strftime('%Y-%m-%d')
                    dialog.destroy()
                except ValueError:
                    messagebox.showerror("Invalid Date", "Please enter a valid date")

            def on_cancel():
                result[0] = None
                dialog.destroy()

            def on_today():
                today = datetime.date.today()
                year_var.set(str(today.year))
                month_var.set(f"{today.month:02d}")
                day_var.set(f"{today.day:02d}")
                result[0] = today.strftime('%Y-%m-%d')
                dialog.destroy()

            # Buttons with modern styling
            tk.Button(btn_frame, text="Today", command=on_today, bg='#17A2B8', fg='white',
                     font=('Arial', 10), padx=15, pady=5).pack(side='left', padx=10)
            tk.Button(btn_frame, text="OK", command=on_ok, bg='#28A745', fg='white',
                     font=('Arial', 10), padx=20, pady=5).pack(side='right', padx=5)
            tk.Button(btn_frame, text="Cancel", command=on_cancel, bg='#DC3545', fg='white',
                     font=('Arial', 10), padx=15, pady=5).pack(side='right', padx=5)

            # Wait for dialog to close
            self.master.wait_window(dialog)

            # Update the date variable if a date was selected
            if result[0]:
                date_var.set(result[0])
                self.log(f"✅ Date selected: {result[0]}")
            else:
                self.log("Calendar dialog cancelled")

        except Exception as e:
            self.log(f"❌ Error opening calendar: {str(e)}")
            messagebox.showerror("Calendar Error", f"Failed to open calendar: {str(e)}")

    # Real Google Ads functionality (from working ads.py)
    def get_oauth_tokens(self):
        """Start OAuth flow to get refresh token"""
        self.log("Starting OAuth flow...")

        def oauth_thread():
            try:
                # Create a temporary file for client secrets
                with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
                    client_config = {
                        "installed": {
                            "client_id": CLIENT_ID,
                            "client_secret": CLIENT_SECRET,
                            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                            "token_uri": "https://oauth2.googleapis.com/token",
                            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                            "redirect_uris": ["urn:ietf:wg:oauth:2.0:oob", "http://localhost"]
                        }
                    }
                    json.dump(client_config, f)
                    client_secrets_file = f.name

                self.log(f"Created temporary client secrets file: {client_secrets_file}")

                # Start OAuth flow
                flow = InstalledAppFlow.from_client_secrets_file(
                    client_secrets_file, SCOPES)
                self.credentials = flow.run_local_server(port=0)

                # Update UI with refresh token
                self.refresh_token_var.set(self.credentials.refresh_token)
                self.log(f"Successfully obtained refresh token: {self.credentials.refresh_token[:10]}...")
                messagebox.showinfo("Success", "OAuth tokens obtained successfully!")

                # Clean up temporary file
                os.unlink(client_secrets_file)

            except Exception as e:
                self.log(f"Error during OAuth flow: {str(e)}")
                messagebox.showerror("OAuth Error", f"Failed to obtain OAuth tokens: {str(e)}")

            finally:
                self.status_var.set("Ready")

        # Run OAuth flow in a separate thread to keep UI responsive
        self.status_var.set("Running OAuth flow...")
        threading.Thread(target=oauth_thread, daemon=True).start()

    def validate_credentials(self):
        """Validate credentials"""
        if not self.customer_id_var.get() or not self.developer_token_var.get() or not self.refresh_token_var.get():
            messagebox.showerror("Missing Credentials", "Please provide all required Google Ads credentials")
            return False
        return True

    def test_api_connection(self):
        """Test API connection"""
        if not self.validate_credentials():
            return

        self.log("Testing API connection...")
        try:
            # Create Google Ads client
            client_config = {
                "client_id": CLIENT_ID,
                "client_secret": CLIENT_SECRET,
                "refresh_token": self.refresh_token_var.get(),
                "developer_token": self.developer_token_var.get(),
                "login_customer_id": self.manager_account_var.get(),
                "use_proto_plus": True
            }

            client = GoogleAdsClient.load_from_dict(client_config)
            customer_id = self.customer_id_var.get().replace("-", "")

            # Simple test query
            ga_service = client.get_service("GoogleAdsService")
            query = "SELECT customer.id FROM customer LIMIT 1"

            search_request = client.get_type("SearchGoogleAdsRequest")
            search_request.customer_id = customer_id
            search_request.query = query

            results = ga_service.search(request=search_request)

            # If we get here, connection is successful
            self.log("✅ API connection test successful!")
            messagebox.showinfo("Success", "API connection test successful!")

        except Exception as e:
            self.log(f"❌ API connection test failed: {str(e)}")
            messagebox.showerror("Connection Error", f"API connection test failed: {str(e)}")

    def start_extraction(self):
        """Start the data extraction process"""
        # Validate inputs
        if not self.validate_credentials():
            return

        def extraction_thread():
            try:
                self.log("Starting data extraction...")
                self.status_var.set("Extracting data...")

                # Get date range from StringVar
                start_date = self.start_date_var.get()
                end_date = self.end_date_var.get()

                # Create Google Ads client
                client_config = {
                    "client_id": CLIENT_ID,
                    "client_secret": CLIENT_SECRET,
                    "refresh_token": self.refresh_token_var.get(),
                    "developer_token": self.developer_token_var.get(),
                    "login_customer_id": self.manager_account_var.get(),
                    "use_proto_plus": True
                }

                client = GoogleAdsClient.load_from_dict(client_config)

                # Create query
                query = f"""
                SELECT
                    campaign.id,
                    campaign.name,
                    metrics.cost_micros,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.conversions,
                    segments.date
                FROM campaign
                WHERE segments.date BETWEEN '{start_date}' AND '{end_date}'
                ORDER BY segments.date
                """

                # Execute query
                ga_service = client.get_service("GoogleAdsService")
                customer_id = self.customer_id_var.get().replace("-", "")

                self.log(f"Executing query for customer ID: {customer_id}")
                self.log(f"Date range: {start_date} to {end_date}")

                # Make the request
                search_request = client.get_type("SearchGoogleAdsRequest")
                search_request.customer_id = customer_id
                search_request.query = query

                results = ga_service.search(request=search_request)

                # Process results
                data = []
                for row in results:
                    campaign_id = row.campaign.id
                    campaign_name = row.campaign.name
                    cost = row.metrics.cost_micros / 1000000  # Convert micros to actual currency
                    impressions = row.metrics.impressions
                    clicks = row.metrics.clicks
                    conversions = row.metrics.conversions
                    date = row.segments.date

                    data.append({
                        'Date': date,
                        'Campaign ID': campaign_id,
                        'Campaign Name': campaign_name,
                        'Cost': cost,
                        'Impressions': impressions,
                        'Clicks': clicks,
                        'Conversions': conversions,
                        'CTR': (clicks / impressions if impressions > 0 else 0),
                        'CPC': (cost / clicks if clicks > 0 else 0),
                        'Conv. Rate': (conversions / clicks if clicks > 0 else 0),
                        'Cost per Conv.': (cost / conversions if conversions > 0 else 0)
                    })

                # Convert to DataFrame
                self.ad_spend_data = pd.DataFrame(data)

                # Display summary
                if not data:
                    self.log("No data found for the specified date range.")
                else:
                    total_cost = self.ad_spend_data['Cost'].sum()
                    total_clicks = self.ad_spend_data['Clicks'].sum()
                    total_impressions = self.ad_spend_data['Impressions'].sum()
                    total_conversions = self.ad_spend_data['Conversions'].sum()

                    self.log(f"✅ Data extraction complete. Found {len(data)} records.")
                    self.log(f"💰 Total Cost: ${total_cost:.2f}")
                    self.log(f"👆 Total Clicks: {total_clicks}")
                    self.log(f"👁️ Total Impressions: {total_impressions}")
                    self.log(f"🎯 Total Conversions: {total_conversions:.2f}")

                    # Update KPI cards if they exist
                    if hasattr(self, 'cost_card'):
                        self.cost_card.update_value(f"${total_cost:.2f}")
                    if hasattr(self, 'clicks_card'):
                        self.clicks_card.update_value(f"{total_clicks:,}")
                    if hasattr(self, 'impressions_card'):
                        self.impressions_card.update_value(f"{total_impressions:,}")
                    if hasattr(self, 'conversion_card'):
                        ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
                        self.conversion_card.update_value(f"{ctr:.2f}%")

                    # Show first few rows in log
                    self.log("\n📊 Sample data (first 3 rows):")
                    sample_data = self.ad_spend_data.head(3)
                    for _, row in sample_data.iterrows():
                        self.log(f"📅 {row['Date']} | 📢 {row['Campaign Name']} | 💰 ${row['Cost']:.2f} | 👆 {row['Clicks']}")

                    # Refresh the data view tab
                    self.refresh_data_view()

                    # Auto-sync to Airtable if enabled
                    if hasattr(self, 'airtable_auto_sync_var') and self.airtable_auto_sync_var.get():
                        api_key = self.airtable_api_key_var.get().strip() if hasattr(self, 'airtable_api_key_var') else ""
                        if api_key:
                            self.log("🔄 Auto-syncing to Airtable...")
                            try:
                                self.airtable_manager.set_api_key(api_key)
                                sync_mode = self.airtable_sync_mode_var.get() if hasattr(self, 'airtable_sync_mode_var') else "incremental"
                                uploaded_count, errors, skipped_count = self.airtable_manager.upload_google_ads_data(
                                    self.ad_spend_data,
                                    mode=sync_mode
                                )
                                if errors:
                                    self.log(f"⚠️ Airtable auto-sync completed with {len(errors)} errors")
                                else:
                                    result_msg = f"✅ Auto-synced {uploaded_count} records to Airtable"
                                    if sync_mode == "incremental" and skipped_count > 0:
                                        result_msg += f" (skipped {skipped_count} existing)"
                                    self.log(result_msg)
                            except Exception as e:
                                self.log(f"❌ Airtable auto-sync failed: {str(e)}")
                        else:
                            self.log("⚠️ Auto-sync enabled but no Airtable API key configured")

                self.status_var.set("Data extraction complete")

            except GoogleAdsException as ex:
                self.log(f"❌ Google Ads API error: {ex}")
                for error in ex.failure.errors:
                    self.log(f"Error: {error.message}")
                messagebox.showerror("Google Ads API Error", f"Failed to extract data: {ex.failure.errors[0].message}")

            except Exception as e:
                self.log(f"❌ Error during data extraction: {str(e)}")
                messagebox.showerror("Extraction Error", f"Failed to extract data: {str(e)}")

            finally:
                self.status_var.set("Ready")

        # Run extraction in a separate thread
        threading.Thread(target=extraction_thread, daemon=True).start()

    def load_csv_data(self):
        """Load data from an existing CSV file"""
        try:
            self.log("📂 Opening file dialog to select CSV file...")

            # Open file dialog to select CSV file
            file_path = filedialog.askopenfilename(
                title="Select CSV file to load",
                filetypes=[
                    ("CSV files", "*.csv"),
                    ("Excel files", "*.xlsx"),
                    ("All files", "*.*")
                ],
                initialdir=os.getcwd()
            )

            if not file_path:
                self.log("File selection cancelled")
                return

            self.log(f"📄 Loading data from: {os.path.basename(file_path)}")
            self.status_var.set("Loading CSV data...")

            # Determine file type and load accordingly
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension == '.csv':
                # Try different encodings for CSV files
                encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                loaded_data = None

                for encoding in encodings:
                    try:
                        loaded_data = pd.read_csv(file_path, encoding=encoding)
                        self.log(f"✅ Successfully loaded CSV with {encoding} encoding")
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        self.log(f"❌ Error with {encoding} encoding: {str(e)}")
                        continue

                if loaded_data is None:
                    raise Exception("Could not load CSV file with any supported encoding")

            elif file_extension in ['.xlsx', '.xls']:
                loaded_data = pd.read_excel(file_path)
                self.log(f"✅ Successfully loaded Excel file")
            else:
                # Try to load as CSV anyway
                loaded_data = pd.read_csv(file_path)
                self.log(f"✅ Successfully loaded file as CSV")

            # Validate and process the loaded data
            if loaded_data.empty:
                raise Exception("The loaded file is empty")

            # Check if it looks like Google Ads data by looking for expected columns
            expected_columns = ['Date', 'Campaign Name', 'Cost', 'Clicks', 'Impressions']
            missing_columns = [col for col in expected_columns if col not in loaded_data.columns]

            if missing_columns:
                self.log(f"⚠️ Warning: Missing expected columns: {missing_columns}")
                self.log("📊 Available columns: " + ", ".join(loaded_data.columns.tolist()))

                # Ask user if they want to continue anyway
                response = messagebox.askyesno(
                    "Column Mismatch",
                    f"The loaded file is missing some expected Google Ads columns:\n\n"
                    f"Missing: {', '.join(missing_columns)}\n"
                    f"Available: {', '.join(loaded_data.columns.tolist())}\n\n"
                    f"Do you want to continue loading this data anyway?"
                )

                if not response:
                    self.log("Data loading cancelled by user")
                    self.status_var.set("Ready")
                    return

            # Store the loaded data
            self.ad_spend_data = loaded_data

            # Calculate summary statistics
            total_records = len(loaded_data)

            # Try to calculate metrics if the columns exist
            summary_stats = {}
            if 'Cost' in loaded_data.columns:
                summary_stats['Total Cost'] = loaded_data['Cost'].sum()
            if 'Clicks' in loaded_data.columns:
                summary_stats['Total Clicks'] = loaded_data['Clicks'].sum()
            if 'Impressions' in loaded_data.columns:
                summary_stats['Total Impressions'] = loaded_data['Impressions'].sum()
            if 'Conversions' in loaded_data.columns:
                summary_stats['Total Conversions'] = loaded_data['Conversions'].sum()

            # Update KPI cards if they exist and we have the data
            if hasattr(self, 'kpi_cards'):
                if 'Total Cost' in summary_stats:
                    self.kpi_cards['cost'].update_value(f"${summary_stats['Total Cost']:,.2f}")
                if 'Total Clicks' in summary_stats:
                    self.kpi_cards['clicks'].update_value(f"{summary_stats['Total Clicks']:,}")
                if 'Total Impressions' in summary_stats:
                    self.kpi_cards['impressions'].update_value(f"{summary_stats['Total Impressions']:,}")
                if 'Total Clicks' in summary_stats and 'Total Impressions' in summary_stats:
                    ctr = (summary_stats['Total Clicks'] / summary_stats['Total Impressions'] * 100) if summary_stats['Total Impressions'] > 0 else 0
                    self.kpi_cards['conv_rate'].update_value(f"{ctr:.2f}%")

            # Log success message with summary
            self.log(f"✅ Data loaded successfully!")
            self.log(f"📊 Records loaded: {total_records:,}")
            self.log(f"📋 Columns: {', '.join(loaded_data.columns.tolist())}")

            # Log summary statistics
            for stat_name, stat_value in summary_stats.items():
                if stat_name == 'Total Cost':
                    self.log(f"💰 {stat_name}: ${stat_value:,.2f}")
                else:
                    self.log(f"📈 {stat_name}: {stat_value:,.0f}")

            # Refresh the data view tab
            self.refresh_data_view()

            # Show success message
            messagebox.showinfo(
                "Data Loaded Successfully",
                f"Successfully loaded {total_records:,} records from:\n{os.path.basename(file_path)}\n\n"
                f"You can now use 'Preview Data' to view the loaded data or check the 'Data View' tab."
            )

            self.status_var.set("Data loaded successfully")

        except Exception as e:
            error_msg = f"Failed to load CSV data: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("Load Error", error_msg)
            self.status_var.set("Ready")

    def preview_data(self):
        """Preview extracted data in a professional table"""
        if not hasattr(self, 'ad_spend_data') or self.ad_spend_data is None or self.ad_spend_data.empty:
            messagebox.showerror("No Data", "Please extract data first before previewing")
            self.log("❌ No data available for preview")
            return

        try:
            self.log("📊 Opening data preview window...")

            # Create preview window
            preview_window = tk.Toplevel(self.master)
            preview_window.title("📊 Google Ads Data Preview")
            preview_window.geometry("1200x700")
            preview_window.transient(self.master)

            # Center the window
            preview_window.update_idletasks()
            x = (preview_window.winfo_screenwidth() // 2) - (1200 // 2)
            y = (preview_window.winfo_screenheight() // 2) - (700 // 2)
            preview_window.geometry(f"1200x700+{x}+{y}")

            # Main frame
            main_frame = ttk_bootstrap.Frame(preview_window, padding=15)
            main_frame.pack(fill=BOTH, expand=True)

            # Header frame with summary info
            header_frame = ttk_bootstrap.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 15))

            # Summary statistics
            total_records = len(self.ad_spend_data)
            total_cost = self.ad_spend_data['Cost'].sum()
            total_clicks = self.ad_spend_data['Clicks'].sum()
            total_impressions = self.ad_spend_data['Impressions'].sum()
            total_conversions = self.ad_spend_data['Conversions'].sum()

            # Summary labels
            summary_frame = ttk_bootstrap.Frame(header_frame)
            summary_frame.pack(fill=X)

            ttk_bootstrap.Label(
                summary_frame,
                text=f"📊 Data Preview - {total_records:,} Records",
                font=('Arial', 14, 'bold'),
                bootstyle="primary"
            ).pack(side=LEFT)

            # Summary stats in a grid
            stats_frame = ttk_bootstrap.Frame(header_frame)
            stats_frame.pack(fill=X, pady=(10, 0))

            # Create summary cards
            stats_data = [
                ("💰 Total Cost", f"${total_cost:,.2f}", "success"),
                ("👆 Total Clicks", f"{total_clicks:,}", "info"),
                ("👁️ Total Impressions", f"{total_impressions:,}", "warning"),
                ("🎯 Total Conversions", f"{total_conversions:,.2f}", "danger")
            ]

            for i, (label, value, style) in enumerate(stats_data):
                stat_frame = ttk_bootstrap.Frame(stats_frame, bootstyle=style, padding=10)
                stat_frame.grid(row=0, column=i, padx=5, sticky='ew')

                ttk_bootstrap.Label(
                    stat_frame,
                    text=label,
                    font=('Arial', 9, 'bold'),
                    bootstyle=f"{style}-inverse"
                ).pack()

                ttk_bootstrap.Label(
                    stat_frame,
                    text=value,
                    font=('Arial', 12, 'bold'),
                    bootstyle=f"{style}-inverse"
                ).pack()

            # Configure grid weights
            for i in range(4):
                stats_frame.columnconfigure(i, weight=1)

            # Table frame with scrollbars
            table_frame = ttk_bootstrap.Frame(main_frame)
            table_frame.pack(fill=BOTH, expand=True, pady=(15, 0))

            # Create treeview with scrollbars
            tree_frame = ttk_bootstrap.Frame(table_frame)
            tree_frame.pack(fill=BOTH, expand=True)

            # Define columns based on actual data
            columns = list(self.ad_spend_data.columns)

            # Create treeview
            tree = ttk_bootstrap.Treeview(
                tree_frame,
                columns=columns,
                show='headings',
                height=20,
                bootstyle="primary"
            )

            # Configure column headings and widths
            column_widths = {
                'Date': 100,
                'Campaign ID': 100,
                'Campaign Name': 250,
                'Cost': 100,
                'Impressions': 100,
                'Clicks': 80,
                'Conversions': 100,
                'CTR': 80,
                'CPC': 80,
                'Conv. Rate': 100,
                'Cost per Conv.': 120
            }

            for col in columns:
                tree.heading(col, text=col, anchor='center')
                width = column_widths.get(col, 120)
                tree.column(col, width=width, anchor='center')

            # Add scrollbars
            v_scrollbar = ttk_bootstrap.Scrollbar(tree_frame, orient=VERTICAL, command=tree.yview)
            h_scrollbar = ttk_bootstrap.Scrollbar(tree_frame, orient=HORIZONTAL, command=tree.xview)
            tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

            # Pack treeview and scrollbars
            tree.grid(row=0, column=0, sticky='nsew')
            v_scrollbar.grid(row=0, column=1, sticky='ns')
            h_scrollbar.grid(row=1, column=0, sticky='ew')

            tree_frame.grid_rowconfigure(0, weight=1)
            tree_frame.grid_columnconfigure(0, weight=1)

            # Populate the treeview with data
            for index, row in self.ad_spend_data.iterrows():
                values = []
                for col in columns:
                    value = row[col]
                    # Format numeric values
                    if col == 'Cost' or col == 'CPC' or col == 'Cost per Conv.':
                        values.append(f"${value:.2f}")
                    elif col == 'CTR' or col == 'Conv. Rate':
                        values.append(f"{value:.2%}")
                    elif col == 'Conversions':
                        values.append(f"{value:.2f}")
                    elif col in ['Clicks', 'Impressions', 'Campaign ID']:
                        values.append(f"{int(value):,}")
                    else:
                        values.append(str(value))

                tree.insert('', 'end', values=values)

            # Button frame
            button_frame = ttk_bootstrap.Frame(main_frame)
            button_frame.pack(fill=X, pady=(15, 0))

            # Export button
            ttk_bootstrap.Button(
                button_frame,
                text="📤 Export Data",
                command=self.export_data,
                bootstyle="success",
                width=15
            ).pack(side=LEFT, padx=5)

            # Refresh button
            ttk_bootstrap.Button(
                button_frame,
                text="🔄 Refresh",
                command=lambda: self.refresh_preview_data(tree),
                bootstyle="info",
                width=15
            ).pack(side=LEFT, padx=5)

            # Close button
            ttk_bootstrap.Button(
                button_frame,
                text="❌ Close",
                command=preview_window.destroy,
                bootstyle="secondary",
                width=15
            ).pack(side=RIGHT, padx=5)

            self.log(f"✅ Data preview opened - showing {total_records:,} records")

        except Exception as e:
            self.log(f"❌ Error opening data preview: {str(e)}")
            messagebox.showerror("Preview Error", f"Failed to open data preview: {str(e)}")

    def refresh_preview_data(self, tree):
        """Refresh the preview data in the treeview"""
        try:
            # Clear existing data
            for item in tree.get_children():
                tree.delete(item)

            # Repopulate with current data
            if hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None:
                columns = list(self.ad_spend_data.columns)
                for index, row in self.ad_spend_data.iterrows():
                    values = []
                    for col in columns:
                        value = row[col]
                        # Format numeric values
                        if col == 'Cost' or col == 'CPC' or col == 'Cost per Conv.':
                            values.append(f"${value:.2f}")
                        elif col == 'CTR' or col == 'Conv. Rate':
                            values.append(f"{value:.2%}")
                        elif col == 'Conversions':
                            values.append(f"{value:.2f}")
                        elif col in ['Clicks', 'Impressions', 'Campaign ID']:
                            values.append(f"{int(value):,}")
                        else:
                            values.append(str(value))

                    tree.insert('', 'end', values=values)

                self.log("✅ Preview data refreshed")
            else:
                self.log("⚠️ No data available to refresh")

        except Exception as e:
            self.log(f"❌ Error refreshing preview: {str(e)}")
            messagebox.showerror("Refresh Error", f"Failed to refresh data: {str(e)}")

    def export_data(self):
        """Export data to CSV or Excel"""
        if not hasattr(self, 'ad_spend_data') or self.ad_spend_data is None:
            messagebox.showerror("No Data", "Please extract data first")
            return

        try:
            export_format = self.export_format_var.get().lower()

            # Create filename with date range
            start_date = self.start_date_var.get().replace("-", "")
            end_date = self.end_date_var.get().replace("-", "")

            if export_format == "csv":
                filename = f"google_ads_data_{start_date}_to_{end_date}.csv"
                self.ad_spend_data.to_csv(filename, index=False)
            elif export_format == "excel":
                filename = f"google_ads_data_{start_date}_to_{end_date}.xlsx"
                self.ad_spend_data.to_excel(filename, index=False, sheet_name="Google Ads Data")
            else:
                filename = f"google_ads_data_{start_date}_to_{end_date}.csv"
                self.ad_spend_data.to_csv(filename, index=False)

            self.log(f"✅ Data exported to: {filename}")
            messagebox.showinfo("Export Success", f"Data exported successfully to {filename}")

        except Exception as e:
            self.log(f"❌ Error exporting data: {str(e)}")
            messagebox.showerror("Export Error", f"Failed to export data: {str(e)}")

    def bulk_export(self):
        """Bulk export for multiple clients"""
        self.log("Starting bulk export...")

        # Get all clients from config
        clients = self.config_manager.config.get("clients", [])
        if not clients:
            messagebox.showinfo("No Clients", "No clients configured for bulk export")
            return

        def bulk_export_thread():
            try:
                self.status_var.set("Running bulk export...")
                successful_exports = 0

                for client in clients:
                    try:
                        client_name = client.get("client_name", "Unknown")
                        customer_id = client.get("customer_id_formatted", "")

                        if not customer_id:
                            self.log(f"⚠️ Skipping {client_name} - no customer ID")
                            continue

                        self.log(f"📊 Exporting data for {client_name}...")

                        # Temporarily set customer ID
                        original_customer_id = self.customer_id_var.get()
                        self.customer_id_var.set(customer_id)

                        # Run extraction for this client
                        # (This would need to be implemented as a synchronous version)
                        self.log(f"✅ Export completed for {client_name}")
                        successful_exports += 1

                        # Restore original customer ID
                        self.customer_id_var.set(original_customer_id)

                    except Exception as e:
                        self.log(f"❌ Error exporting {client_name}: {str(e)}")

                self.log(f"🎉 Bulk export completed. {successful_exports}/{len(clients)} clients exported successfully.")
                messagebox.showinfo("Bulk Export Complete", f"Exported data for {successful_exports} out of {len(clients)} clients")

            except Exception as e:
                self.log(f"❌ Bulk export error: {str(e)}")
                messagebox.showerror("Bulk Export Error", f"Bulk export failed: {str(e)}")
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=bulk_export_thread, daemon=True).start()

    def test_airtable_connection(self):
        """Test Airtable API connection"""
        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        self.airtable_manager.set_api_key(api_key)
        success, message = self.airtable_manager.test_connection()

        if success:
            self.log("✅ Airtable connection successful!")
            self.airtable_log(f"✅ Connection test successful: {message}")
            messagebox.showinfo("Connection Success", "Successfully connected to Airtable!")
        else:
            self.log(f"❌ Airtable connection failed: {message}")
            self.airtable_log(f"❌ Connection test failed: {message}")
            messagebox.showerror("Connection Failed", f"Failed to connect to Airtable:\n{message}")

    def sync_to_airtable(self):
        """Sync current Google Ads data to Airtable"""
        if not hasattr(self, 'ad_spend_data') or self.ad_spend_data is None or self.ad_spend_data.empty:
            messagebox.showerror("No Data", "Please extract Google Ads data first before syncing to Airtable")
            return

        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        def sync_thread():
            try:
                self.airtable_log("🔄 Starting Airtable sync...")
                self.status_var.set("Syncing to Airtable...")

                # Set API key
                self.airtable_manager.set_api_key(api_key)

                # Test connection first
                success, message = self.airtable_manager.test_connection()
                if not success:
                    self.airtable_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Sync Failed", f"Connection failed: {message}")
                    return

                # Get sync mode
                sync_mode = self.airtable_sync_mode_var.get() if hasattr(self, 'airtable_sync_mode_var') else "append"

                # Clear existing data if in replace mode
                if sync_mode == "replace":
                    self.airtable_log("🗑️ Clearing existing Airtable data...")
                    existing_records = self.airtable_manager.get_existing_records(get_all=True)
                    if existing_records:
                        record_ids = [record['id'] for record in existing_records]
                        if self.airtable_manager.delete_records(record_ids):
                            self.airtable_log(f"✅ Cleared {len(record_ids)} existing records")
                        else:
                            self.airtable_log("⚠️ Failed to clear some existing records")

                # Upload data with selected mode
                if sync_mode == "incremental":
                    self.airtable_log(f"🔍 Checking for new records in {len(self.ad_spend_data)} extracted records...")
                    latest_date = self.airtable_manager.get_latest_date_in_airtable()
                    if latest_date:
                        self.airtable_log(f"📅 Latest date in Airtable: {latest_date}")
                else:
                    self.airtable_log(f"📤 Uploading {len(self.ad_spend_data)} records in {sync_mode} mode...")

                uploaded_count, errors, skipped_count = self.airtable_manager.upload_google_ads_data(
                    self.ad_spend_data,
                    mode=sync_mode
                )

                # Report results
                if sync_mode == "incremental" and skipped_count > 0:
                    self.airtable_log(f"⏭️ Skipped {skipped_count} existing records")

                if errors:
                    self.airtable_log(f"⚠️ Upload completed with errors:")
                    for error in errors:
                        self.airtable_log(f"   {error}")
                    result_msg = f"Uploaded {uploaded_count} records with {len(errors)} errors"
                    if sync_mode == "incremental":
                        result_msg += f" (skipped {skipped_count} existing)"
                else:
                    self.airtable_log(f"✅ Successfully uploaded {uploaded_count} records!")
                    result_msg = f"Successfully uploaded {uploaded_count} records"
                    if sync_mode == "incremental" and skipped_count > 0:
                        result_msg += f" (skipped {skipped_count} existing)"

                self.log(f"✅ Airtable sync completed: {result_msg}")
                messagebox.showinfo("Sync Complete", result_msg)

            except Exception as e:
                error_msg = f"Sync failed: {str(e)}"
                self.airtable_log(f"❌ {error_msg}")
                self.log(f"❌ Airtable sync error: {str(e)}")
                messagebox.showerror("Sync Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=sync_thread, daemon=True).start()

    def view_airtable_data(self):
        """Open Airtable in browser to view data"""
        import webbrowser
        airtable_url = f"https://airtable.com/{AIRTABLE_BASE_ID}/{AIRTABLE_GOOGLE_ADS_TABLE_ID}"
        webbrowser.open(airtable_url)
        self.airtable_log("🌐 Opened Airtable in browser")

    def clear_airtable_data(self):
        """Clear all data from Airtable Google Ads table"""
        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        # Confirm action
        if not messagebox.askyesno("Confirm Clear",
                                  "This will delete ALL Google Ads data from your Airtable table.\n\n"
                                  "This action cannot be undone. Are you sure?"):
            return

        def clear_thread():
            try:
                self.airtable_log("🗑️ Clearing all Airtable data...")
                self.status_var.set("Clearing Airtable data...")

                # Set API key and test connection
                self.airtable_manager.set_api_key(api_key)
                success, message = self.airtable_manager.test_connection()
                if not success:
                    self.airtable_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Clear Failed", f"Connection failed: {message}")
                    return

                # Get all records
                existing_records = self.airtable_manager.get_existing_records()
                if not existing_records:
                    self.airtable_log("ℹ️ No records found to clear")
                    messagebox.showinfo("Clear Complete", "No records found to clear")
                    return

                # Delete all records
                record_ids = [record['id'] for record in existing_records]
                if self.airtable_manager.delete_records(record_ids):
                    self.airtable_log(f"✅ Successfully cleared {len(record_ids)} records")
                    self.log(f"✅ Cleared {len(record_ids)} records from Airtable")
                    messagebox.showinfo("Clear Complete", f"Successfully cleared {len(record_ids)} records from Airtable")
                else:
                    self.airtable_log("❌ Failed to clear records")
                    messagebox.showerror("Clear Failed", "Failed to clear records from Airtable")

            except Exception as e:
                error_msg = f"Clear failed: {str(e)}"
                self.airtable_log(f"❌ {error_msg}")
                self.log(f"❌ Airtable clear error: {str(e)}")
                messagebox.showerror("Clear Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=clear_thread, daemon=True).start()

    def show_airtable_stats(self):
        """Show Airtable statistics"""
        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        def stats_thread():
            try:
                self.airtable_log("📊 Fetching Airtable statistics...")
                self.status_var.set("Fetching statistics...")

                # Set API key and test connection
                self.airtable_manager.set_api_key(api_key)
                success, message = self.airtable_manager.test_connection()
                if not success:
                    self.airtable_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Stats Failed", f"Connection failed: {message}")
                    return

                # Get all records
                all_records = self.airtable_manager.get_existing_records(get_all=True)
                total_records = len(all_records)

                if total_records == 0:
                    self.airtable_log("ℹ️ No records found in Airtable")
                    messagebox.showinfo("Statistics", "No records found in Airtable")
                    return

                # Calculate statistics
                latest_date = self.airtable_manager.get_latest_date_in_airtable()

                # Count unique campaigns
                campaigns = set()
                total_cost = 0
                total_clicks = 0
                total_impressions = 0

                for record in all_records:
                    fields = record.get('fields', {})
                    campaign_name = fields.get('Campaign Name')
                    if campaign_name:
                        campaigns.add(campaign_name)

                    cost = fields.get('Cost', 0)
                    clicks = fields.get('Clicks', 0)
                    impressions = fields.get('Impressions', 0)

                    if isinstance(cost, (int, float)):
                        total_cost += cost
                    if isinstance(clicks, (int, float)):
                        total_clicks += clicks
                    if isinstance(impressions, (int, float)):
                        total_impressions += impressions

                # Format statistics
                stats_message = f"""📊 Airtable Statistics:

📈 Records: {total_records:,}
🎯 Unique Campaigns: {len(campaigns):,}
📅 Latest Date: {latest_date or 'N/A'}

💰 Total Cost: ${total_cost:,.2f}
👆 Total Clicks: {total_clicks:,}
👁️ Total Impressions: {total_impressions:,}

📊 Average CTR: {(total_clicks/total_impressions*100):.2f}% (if impressions > 0)
💵 Average CPC: ${(total_cost/total_clicks):.2f} (if clicks > 0)"""

                self.airtable_log("✅ Statistics retrieved successfully")
                messagebox.showinfo("Airtable Statistics", stats_message)

            except Exception as e:
                error_msg = f"Failed to get statistics: {str(e)}"
                self.airtable_log(f"❌ {error_msg}")
                messagebox.showerror("Statistics Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=stats_thread, daemon=True).start()

    def airtable_log(self, message):
        """Add message to Airtable status log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        if hasattr(self, 'airtable_status_text'):
            self.airtable_status_text.insert(tk.END, formatted_message + "\n")
            self.airtable_status_text.see(tk.END)

    def browse_ghl_file(self):
        """Browse for GHL CSV file"""
        file_path = filedialog.askopenfilename(
            title="Select GHL CSV File",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            self.ghl_file_var.set(file_path)
            self.ghl_log(f"📁 Selected file: {file_path}")

    def load_default_ghl_file(self):
        """Load the default quickfix - ghl.csv file"""
        default_file = "quickfix - ghl.csv"
        if os.path.exists(default_file):
            self.ghl_file_var.set(default_file)
            self.ghl_log(f"⚡ Loaded default file: {default_file}")
            self.preview_ghl_data()
        else:
            messagebox.showerror("File Not Found", f"Default file '{default_file}' not found in current directory")

    def preview_ghl_data(self):
        """Preview GHL data from selected CSV file"""
        file_path = self.ghl_file_var.get().strip()
        if not file_path:
            messagebox.showerror("No File Selected", "Please select a GHL CSV file first")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("File Not Found", f"File not found: {file_path}")
            return

        try:
            self.ghl_log(f"📖 Loading data from: {file_path}")

            # Read CSV file
            self.ghl_data = pd.read_csv(file_path)

            # Check if this is actually GHL data or Google Ads data
            ghl_columns = ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created']
            ads_columns = ['Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks']

            # Check which type of data this is
            if any(col in self.ghl_data.columns for col in ads_columns):
                self.ghl_log("⚠️ Warning: This appears to be Google Ads data, not GHL lead data")
                messagebox.showwarning("Data Type Mismatch",
                                     "This appears to be Google Ads data, not GHL lead data.\n"
                                     "Please select a proper GHL CSV file with lead/contact data.")
                return

            # Clear existing data in treeview
            for item in self.ghl_tree.get_children():
                self.ghl_tree.delete(item)

            # Show first 50 rows for preview
            preview_data = self.ghl_data.head(50)

            for _, row in preview_data.iterrows():
                values = []
                for col in ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created']:
                    value = row.get(col, '')
                    if pd.isna(value):
                        value = ''
                    values.append(str(value))

                self.ghl_tree.insert('', 'end', values=values)

            self.ghl_log(f"✅ Loaded {len(self.ghl_data)} records for preview (showing first 50)")
            self.log(f"GHL data loaded: {len(self.ghl_data)} records")

        except Exception as e:
            error_msg = f"Failed to load GHL data: {str(e)}"
            self.ghl_log(f"❌ {error_msg}")
            messagebox.showerror("Load Error", error_msg)

    def sync_ghl_to_airtable(self):
        """Sync GHL data to Airtable"""
        if self.ghl_data is None or self.ghl_data.empty:
            messagebox.showerror("No Data", "Please load GHL data first")
            return

        api_key = self.airtable_api_key_var.get().strip() if hasattr(self, 'airtable_api_key_var') else ""
        if not api_key:
            messagebox.showerror("Missing API Key", "Please configure your Airtable API key in the Airtable tab")
            return

        def sync_thread():
            try:
                self.ghl_log("🔄 Starting GHL data sync to Airtable...")
                self.status_var.set("Syncing GHL data...")

                # Set API key
                self.airtable_manager.set_api_key(api_key)

                # Test connection first
                success, message = self.airtable_manager.test_connection()
                if not success:
                    self.ghl_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Sync Failed", f"Connection failed: {message}")
                    return

                # Get sync mode (default to incremental for GHL)
                sync_mode = "incremental"  # Always use incremental for GHL to avoid duplicates

                self.ghl_log(f"🔍 Checking for new records in {len(self.ghl_data)} GHL records...")

                uploaded_count, errors, skipped_count = self.airtable_manager.upload_ghl_data(
                    self.ghl_data,
                    mode=sync_mode
                )

                # Report results
                if skipped_count > 0:
                    self.ghl_log(f"⏭️ Skipped {skipped_count} existing records")

                if errors:
                    self.ghl_log(f"⚠️ Upload completed with errors:")
                    for error in errors:
                        self.ghl_log(f"   {error}")
                    result_msg = f"Uploaded {uploaded_count} records with {len(errors)} errors"
                    if skipped_count > 0:
                        result_msg += f" (skipped {skipped_count} existing)"
                else:
                    self.ghl_log(f"✅ Successfully uploaded {uploaded_count} records!")
                    result_msg = f"Successfully uploaded {uploaded_count} records"
                    if skipped_count > 0:
                        result_msg += f" (skipped {skipped_count} existing)"

                self.log(f"✅ GHL sync completed: {result_msg}")
                messagebox.showinfo("Sync Complete", result_msg)

            except Exception as e:
                error_msg = f"GHL sync failed: {str(e)}"
                self.ghl_log(f"❌ {error_msg}")
                self.log(f"❌ GHL sync error: {str(e)}")
                messagebox.showerror("Sync Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=sync_thread, daemon=True).start()

    def view_ghl_airtable(self):
        """Open GHL table in Airtable"""
        import webbrowser
        airtable_url = f"https://airtable.com/{AIRTABLE_BASE_ID}/{self.airtable_manager.ghl_table_id}"
        webbrowser.open(airtable_url)
        self.ghl_log("🌐 Opened GHL table in browser")

    def ghl_log(self, message):
        """Add message to GHL status log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        if hasattr(self, 'ghl_status_text'):
            self.ghl_status_text.insert(tk.END, formatted_message + "\n")
            self.ghl_status_text.see(tk.END)

    def quick_extract(self):
        """Quick extract (placeholder)"""
        self.log("Quick extract started...")
        messagebox.showinfo("Info", "Quick extract - Feature coming soon!")

    def generate_report(self):
        """Generate report (placeholder)"""
        self.log("Generating report...")
        messagebox.showinfo("Info", "Report generation - Feature coming soon!")

    def quick_export(self):
        """Quick export (placeholder)"""
        self.log("Quick export started...")
        messagebox.showinfo("Info", "Quick export - Feature coming soon!")

    def refresh_data(self):
        """Refresh data (placeholder)"""
        self.log("Refreshing data...")
        messagebox.showinfo("Info", "Data refresh - Feature coming soon!")

    def update_chart(self, event=None):
        """Update chart (placeholder)"""
        self.log("Updating chart...")
        # Chart update logic will be implemented

    def add_client_dialog(self):
        """Add client dialog (placeholder)"""
        self.log("Add client dialog...")
        messagebox.showinfo("Info", "Add client - Feature coming soon!")

    def edit_client_dialog(self):
        """Edit client dialog (placeholder)"""
        self.log("Edit client dialog...")
        messagebox.showinfo("Info", "Edit client - Feature coming soon!")

    def remove_client_dialog(self):
        """Remove client dialog (placeholder)"""
        self.log("Remove client dialog...")
        messagebox.showinfo("Info", "Remove client - Feature coming soon!")

    def add_schedule_dialog(self):
        """Add schedule dialog (placeholder)"""
        self.log("Add schedule dialog...")
        messagebox.showinfo("Info", "Add schedule - Feature coming soon!")

    def edit_schedule_dialog(self):
        """Edit schedule dialog (placeholder)"""
        self.log("Edit schedule dialog...")
        messagebox.showinfo("Info", "Edit schedule - Feature coming soon!")

    def remove_schedule_dialog(self):
        """Remove schedule dialog (placeholder)"""
        self.log("Remove schedule dialog...")
        messagebox.showinfo("Info", "Remove schedule - Feature coming soon!")

    def run_schedule_now(self):
        """Run schedule now (placeholder)"""
        self.log("Running schedule now...")
        messagebox.showinfo("Info", "Run schedule - Feature coming soon!")

    def start_scheduler(self):
        """Start scheduler (placeholder)"""
        self.log("Scheduler started")

    def new_config(self):
        """New configuration"""
        if messagebox.askyesno("New Configuration", "This will reset all settings. Continue?"):
            self.config_manager.config = self.config_manager.default_config()
            self.load_settings()
            self.log("Configuration reset to defaults")

    def export_config(self):
        """Export configuration"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.config_manager.config, f, indent=4)
                self.log(f"Configuration exported to {filename}")
                messagebox.showinfo("Success", f"Configuration exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export configuration: {str(e)}")

    def import_config(self):
        """Import configuration"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    imported_config = json.load(f)
                self.config_manager.config = self.config_manager.validate_config(imported_config)
                self.load_settings()
                self.log(f"Configuration imported from {filename}")
                messagebox.showinfo("Success", f"Configuration imported from {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to import configuration: {str(e)}")

    def reset_config(self):
        """Reset configuration to defaults"""
        if messagebox.askyesno("Reset Configuration", "This will reset all settings to defaults. Continue?"):
            self.config_manager.config = self.config_manager.default_config()
            self.config_manager.save_config()
            self.load_settings()
            self.log("Configuration reset to defaults")

    def open_schedule_manager(self):
        """Open schedule manager (placeholder)"""
        self.log("Opening schedule manager...")
        messagebox.showinfo("Info", "Schedule manager - Feature coming soon!")

    def show_help(self):
        """Show help dialog"""
        help_text = """
Google Ads Data Extractor - Professional Edition

This modern application helps you extract and manage Google Ads data with:

🔐 Secure credential management
📊 Advanced data visualization
📤 Multiple export formats
🗃️ Airtable integration & auto-sync
⏰ Automated scheduling
🎨 Modern themes and UI

Airtable Integration:
• Sync data directly to your Airtable workspace
• Auto-sync after data extraction
• Replace or append data options
• Real-time sync status monitoring

For support, visit: https://github.com/your-repo
        """
        messagebox.showinfo("Help", help_text)

    def open_api_docs(self):
        """Open API documentation"""
        import webbrowser
        webbrowser.open("https://developers.google.com/google-ads/api/docs")

    def show_about(self):
        """Show about dialog"""
        about_text = """
Google Ads Data Extractor
Professional Edition v2.0

Built with:
• Python 3.x
• ttkbootstrap for modern UI
• Google Ads API
• Matplotlib for charts

© 2024 - Professional Google Ads Management Tool
        """
        messagebox.showinfo("About", about_text)

    def on_closing(self):
        """Handle application closing"""
        if self.auto_save_var.get():
            self.save_settings()

        self.log("Application closing...")
        self.master.destroy()

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = ModernGoogleAdsExtractor(root)

    # Center the window
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()